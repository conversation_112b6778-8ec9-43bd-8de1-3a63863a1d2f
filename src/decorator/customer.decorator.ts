import { createParamDecorator, ExecutionContext } from '@nestjs/common';
import { GqlExecutionContext } from '@nestjs/graphql';

export interface CustomerInfo {
  id: number;
  email: string;
}

export const Customer = createParamDecorator(
  (data: unknown, context: ExecutionContext) => {
    const ctx = GqlExecutionContext.create(context);
    return context.getType() === 'http'
      ? context.switchToHttp().getRequest().customer
      : ctx.getContext().req.customer;
  },
);

import { Injectable, BadRequestException } from '@nestjs/common';
import { S3Service } from 'src/utils/s3service';
import { UploadImageDto, UploadImageResponseDto } from './dto/upload-image.dto';
import { v4 as uuidv4 } from 'uuid';
import * as path from 'path';

@Injectable()
export class UploadService {
  constructor(private readonly s3Service: S3Service) {}

  async uploadImage(
    uploadDto: UploadImageDto,
    file: Express.Multer.File,
  ): Promise<UploadImageResponseDto> {
    if (!file) {
      throw new BadRequestException('No file provided');
    }

    // Validate file type (only images)
    const allowedMimeTypes = [
      'image/jpeg',
      'image/jpg',
      'image/png',
      'image/gif',
      'image/webp',
      'image/svg+xml',
    ];

    if (!allowedMimeTypes.includes(file.mimetype)) {
      throw new BadRequestException(
        'Invalid file type. Only image files are allowed.',
      );
    }

    try {
      // Generate unique filename to avoid conflicts
      const fileExtension = path.extname(file.originalname);
      const uniqueFileName = `${uuidv4()}${fileExtension}`;
      
      // Construct the full path for S3
      const s3Path = uploadDto.path ? `${uploadDto.path}/${uniqueFileName}` : uniqueFileName;
      
      // Create a modified file object with the unique name
      const modifiedFile: Express.Multer.File = {
        ...file,
        originalname: uniqueFileName,
      };

      // Upload to S3 using the existing service
      const uploadedUrls = await this.s3Service.uploadFilesToS3(
        [modifiedFile],
        uploadDto.path,
      );

      if (!uploadedUrls || uploadedUrls.length === 0) {
        throw new BadRequestException('Failed to upload file to S3');
      }

      return new UploadImageResponseDto(uploadedUrls[0]);
    } catch (error) {
      throw new BadRequestException(
        `Failed to upload image: ${error.message}`,
      );
    }
  }
}

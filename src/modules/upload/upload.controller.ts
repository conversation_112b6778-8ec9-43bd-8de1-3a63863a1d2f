import {
  Controller,
  Post,
  Body,
  UploadedFile,
  UseInterceptors,
  BadRequestException,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { UploadService } from './upload.service';
import { UploadImageDto, UploadImageResponseDto } from './dto/upload-image.dto';

@Controller('catalog-product/upload')
export class UploadController {
  constructor(private readonly uploadService: UploadService) {}

  @Post('image')
  @UseInterceptors(FileInterceptor('files[0]'))
  async uploadImage(
    @Body() uploadDto: UploadImageDto,
    @UploadedFile() file: Express.Multer.File,
  ): Promise<UploadImageResponseDto> {
    if (!file) {
      throw new BadRequestException('No file provided in files[0]');
    }

    return this.uploadService.uploadImage(uploadDto, file);
  }
}

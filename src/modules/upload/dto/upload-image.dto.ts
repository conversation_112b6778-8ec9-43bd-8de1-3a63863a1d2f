import { IsString, IsOptional, IsBoolean } from 'class-validator';
import { Transform } from 'class-transformer';

export class UploadImageDto {
  @IsString()
  path: string;

  @IsString()
  source: string;

  @IsOptional()
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      return value === 'true' || value === '1';
    }
    return Boolean(value);
  })
  @IsBoolean()
  default?: boolean;
}

export class UploadImageResponseDto {
  uploaded_url: string;

  constructor(uploadedUrl: string) {
    this.uploaded_url = uploadedUrl;
  }
}

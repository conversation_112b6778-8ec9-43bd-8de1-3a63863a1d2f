import {
  Body,
  Controller,
  Get,
  Put,
  UseGuards,
  ValidationPipe,
} from '@nestjs/common';
import { CatalogSyncService } from './catalog-sync.service';
import { ApiKeyGuard } from 'src/guards/api-key.guards';
import { UpdateOutboxDto } from './dtos/event-ack-dto';

@UseGuards(ApiKeyGuard)
@Controller('/v1/catalog-admin/sync')
export class CatalogSyncController {
  constructor(private readonly catalogSyncService: CatalogSyncService) {}

  @Put('acknowledge')
  async updateOutboxTable(
    @Body(new ValidationPipe({ whitelist: true, forbidNonWhitelisted: true }))
    updateOutboxDto: UpdateOutboxDto,
  ) {
    const { event_ids, entityType } = updateOutboxDto;
    return this.catalogSyncService.updateOutboxTable(event_ids, entityType);
  }

  @Get('/products')
  async productSync() {
    this.catalogSyncService.productSync();
  }
  @Get('/category')
  async catgoeySync() {
    this.catalogSyncService.categorySync();
  }

  @Get('/url-rewrite')
  async urlRewriteSync() {
    this.catalogSyncService.urlRewriteSync();
  }
}

import {
  BadRequestException,
  Injectable,
  InternalServerErrorException,
} from '@nestjs/common';
import {
  EntityType,
  EventsOutbox,
} from 'src/database/entities/outbox/event-outbox.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import env from 'src/config/env';
import { LoggerService } from 'src/utils/logger-service';
import { CatalogCategoryService } from '../main-catalog/catalog-category/catalog-category.service';
import { ExternalApiHelper } from 'src/utils/external-api-helper';
import { CatalogProductService } from '../main-catalog/catalog-product/catalog-product.service';
import { Cron } from '@nestjs/schedule';

@Injectable()
export class CatalogSyncService {
  constructor(
    @InjectRepository(EventsOutbox)
    private readonly eventOurBoxRepo: Repository<EventsOutbox>,
    private readonly logger: LoggerService,
    private readonly catalogCategoryService: CatalogCategoryService,
    private readonly catalogProductService: CatalogProductService,

    private readonly externalApiHelper: ExternalApiHelper,
  ) {}

  async updateOutboxTable(event_ids: number[], entityType: EntityType) {
    try {
      const ack = await this.eventOurBoxRepo
        .createQueryBuilder()
        .update()
        .set({ received_by_es: true })
        .where('id IN (:...ids)', { ids: event_ids })
        .andWhere('entity_type = :entityType', {
          entityType: entityType,
        })
        .execute();

      return {
        isError: false,
        message: 'Outbox Table updated successfully',
        ids: event_ids,
        data: ack,
      };
    } catch (e) {
      this.logger.error('Failed to update outbox table ', e);
      return {
        isError: true,
        message: 'Failed to update outbox table ',
        error: e?.message || e,
      };
    }
  }

  private async getPendingEvents(entityType: EntityType) {
    const data = this.eventOurBoxRepo
      .createQueryBuilder('events_outbox')
      .select('GROUP_CONCAT(events_outbox.id)', 'event_id')
      .addSelect('events_outbox.entity_id')
      .where(
        '(events_outbox.is_published = :isPublished OR events_outbox.received_by_es = :receivedByEs)',
        { isPublished: 0, receivedByEs: 0 },
      )
      .andWhere('events_outbox.process_count < :processCount', {
        processCount: env.outbox_api_constants.max_process_count,
      })
      .andWhere('events_outbox.entity_type = :entityType', {
        entityType: entityType,
      })
      .andWhere('events_outbox.updated_at < DATE_SUB(NOW(), INTERVAL 5 MINUTE)')
      .groupBy('events_outbox.entity_id')
      .orderBy('events_outbox.id', 'DESC')
      .limit(env.outbox_api_constants.products_count)
      .getRawMany();
    return data;
  }

  private async processCategoryNotification(
    category: any,
    outboxIds: number[],
  ) {
    // Update outbox entries and send notification
    await Promise.all([
      this.updateOutboxEntries(outboxIds),
      this.externalApiHelper.sendCategoryInMongoDbEventBus(category, outboxIds),
    ]);
  }

  private async processProductNotification(
    product: Record<string, any>,
    outboxIds: number[],
  ) {
    // Update outbox entries and send notification
    await Promise.all([
      this.updateOutboxEntries(outboxIds),
      this.catalogProductService.syncProductwithMongo(product, outboxIds),
    ]);
  }

  private async processUrlRewriteNotification(
    data: Record<string, any>,
    outboxIds: number[],
  ) {
    // Update outbox entries and send notification
    await Promise.all([
      this.updateOutboxEntries(outboxIds),
      this.externalApiHelper.notifyUrlRewriteData(data, outboxIds),
    ]);
  }

  private mapOutboxEvents(
    results: { events_outbox_entity_id: number; event_id: string }[],
  ): Record<number, number[]> {
    return results.reduce(
      (map, event) => {
        map[event.events_outbox_entity_id] = event.event_id
          .split(',')
          .map(Number);
        return map;
      },
      {} as Record<number, number[]>,
    );
  }

  private async updateOutboxEntries(outboxIds: number[]) {
    await this.eventOurBoxRepo
      .createQueryBuilder()
      .update()
      .set({
        is_published: true,
        process_count: () => 'process_count + 1',
      })
      .where('id IN (:...ids)', { ids: outboxIds })
      .execute();
  }

  async categorySync() {
    try {
      console.log('+++Category_cron_staretd+++++');

      const results = await this.getPendingEvents(EntityType.CATEGORY);

      if (results?.length === 0) return;

      // Extract category IDs and outbox mapping
      const categoryIds = results.map((e) => e.events_outbox_entity_id);
      const outboxMap = this.mapOutboxEvents(results);

      // Fetch notification data for categories
      const notificationData =
        await this.catalogCategoryService.buildCategoryErpNotificationDataV2(
          categoryIds,
        );

      if (!notificationData?.length) return;

      console.log(
        '+++Category_sync_data+++++',
        JSON.stringify(notificationData),
      );

      // Process notifications and update outbox
      await Promise.all(
        notificationData.map(async (category) => {
          const outboxIds = outboxMap[+category.entity_id] || [];
          return this.processCategoryNotification(category, outboxIds);
        }),
      );

      console.log('+++Category_sync_finished+++++');

      return {
        message: 'Category sync completed',
        processed: results.length,
        results,
      };
    } catch (err) {
      console.log('Error_Category_sync_data:', err);
      this.logger.error('Failed to process category sync cron', err);
    }
  }

  async productSync() {
    try {
      console.log('+++Product_cron_started+++++');
      const results = await this.getPendingEvents(EntityType.PRODUCT);
      if (results?.length === 0) return;

      const productIds = results.map((e) => e.events_outbox_entity_id);
      const outboxMap = this.mapOutboxEvents(results);
      const productDetails = await this.catalogProductService.getProductsByIds(
        productIds,
        null,
        null,
        null,
      );

      if (!productDetails?.items?.length) return;

      console.log('+++Product_sync_data+++++', JSON.stringify(productDetails));

      await Promise.all(
        productDetails.items.map(async (product) => {
          const outboxIds = outboxMap[+product.id] || [];
          return this.processProductNotification(product, outboxIds);
        }),
      );

      console.log('+++Product_sync_finished+++++');

      return {
        message: 'Product sync completed',
        processed: results.length,
        results,
      };
    } catch (err) {
      console.log('+++Error_Product_sync_data+++++', err);
      this.logger.error(
        'Failed to push events in event bus for product sync',
        err,
      );
    }
  }

  async urlRewriteSync() {
    try {
      console.log('+++urlRewriteSync_cron_started+++++');

      const results = await this.getPendingEvents(EntityType.URL_REWRITE);
      if (results?.length === 0) return;

      const urlRewriteIds = results.map((e) => e.events_outbox_entity_id);
      const outboxMap = this.mapOutboxEvents(results);
      const urlRewritesData =
        await this.catalogProductService.getUrlRewriteByIds(urlRewriteIds);

      console.log(
        '+++urlRewriteSync_data+++++',
        JSON.stringify(urlRewritesData),
      );

      if (!urlRewritesData?.length) return;

      await Promise.all(
        urlRewritesData.map(async (rewrite) => {
          const outboxIds = outboxMap[+rewrite.id] || [];
          return this.processUrlRewriteNotification(rewrite, outboxIds);
        }),
      );

      console.log('+++urlRewriteSync_finished+++++');

      return {
        message: 'Product sync completed',
        processed: results.length,
        results,
      };
    } catch (err) {
      console.log('+++urlRewriteSync_Error++++', err);

      this.logger.error(
        'Failed to push events in event bus for product sync',
        err,
      );
    }
  }
}

import { Modu<PERSON> } from '@nestjs/common';
import { CatalogSyncService } from './catalog-sync.service';
import { CatalogSyncController } from './catalog-sync.controller';
import { CatalogCategoryModule } from '../main-catalog/catalog-category/catalog-category.module';
import { UtilsModule } from 'src/utils/utils.module';
import { TypeOrmModule } from '@nestjs/typeorm';
import { EventsOutbox } from 'src/database/entities/outbox/event-outbox.entity';
import { CatalogProductModule } from '../main-catalog/catalog-product/catalog-product.module';

@Module({
  providers: [CatalogSyncService],
  controllers: [CatalogSyncController],
  imports: [
    TypeOrmModule.forFeature([EventsOutbox]),
    CatalogCategoryModule,
    UtilsModule,
    CatalogProductModule,
  ],
})
export class CatalogSyncModule {}

import {
  Controller,
  Post,
  Body,
  Param,
  ValidationPipe,
  Get,
  Query,
  ParseIntPipe,
  Delete,
  Patch,
} from '@nestjs/common';
import { ProductsTagService } from './products-tag.service';
import { CreateProductsTagDto } from './dtos/create-tag-dto';
import { UpdateProductsDto } from './dtos/update-tag-dto';
import { PaginationDto } from './dtos/pagination-dto';
import { ProductsTag } from 'src/database/entities/product/product-tag.entity';
import { FilterProductsDto } from '../catalog-product/dto/filter-product.dto';
import { BulkUpdateDto } from '../product-attachment/dtos/bulk-update.dto';
import { BulkDeleteDto } from '../product-attachment/dtos/bulk-delete.dto';
import { SyncProductTagAttachmentMapper } from 'src/mapper/tag-attachment-sync-mapper';
import { ExternalApiHelper } from 'src/utils/external-api-helper';
import {
  PRODUCT_TAG_EVENT_BUS_INFO,
  EVENT_BUS_ACTIONS,
} from 'src/config/constants';
@Controller('/v1/catalog-admin/tags')
export class ProductsTagController {
  constructor(
    private readonly tagService: ProductsTagService,
    private readonly externalApiHelper: ExternalApiHelper,
    private readonly syncProductTagAttachmentMapper: SyncProductTagAttachmentMapper,
  ) {}

  @Post()
  async create(
    @Body(new ValidationPipe({ transform: true }))
    createTagDto: CreateProductsTagDto,
  ): Promise<CreateProductsTagDto> {
    const data = await this.tagService.createProductTag(createTagDto);

    const mappedData =
      this.syncProductTagAttachmentMapper.syncProductsTagMapper(
        data,
        EVENT_BUS_ACTIONS.CREATE,
      );

    this.externalApiHelper.syncProductAttributes(
      mappedData,
      PRODUCT_TAG_EVENT_BUS_INFO.detailType,
      PRODUCT_TAG_EVENT_BUS_INFO.source,
    );
    return data;
  }

  @Post(':id/products')
  async updateTag(
    @Param('id', ParseIntPipe) id: number,
    @Body(new ValidationPipe({ transform: true }))
    productUpdateIds: UpdateProductsDto,
  ): Promise<ProductsTag> {
    const { addProductIds, removeProductIds, ...rest } = productUpdateIds;
    const data = await this.tagService.updateTag(
      id,
      rest,
      addProductIds,
      removeProductIds,
    );

    const mappedData =
      this.syncProductTagAttachmentMapper.syncProductsTagMapper(
        data,
        EVENT_BUS_ACTIONS.UPDATE,
      );

    this.externalApiHelper.syncProductAttributes(
      mappedData,
      PRODUCT_TAG_EVENT_BUS_INFO.detailType,
      PRODUCT_TAG_EVENT_BUS_INFO.source,
    );

    return data;
  }

  @Delete('bulk-delete')
  async bulkDelete(
    @Body(new ValidationPipe({ transform: true })) bulkDeleteDto: BulkDeleteDto,
  ) {
    const { ids } = bulkDeleteDto;
    const data = await this.tagService.bulkDelete(ids);
    this.externalApiHelper.syncProductAttributes(
      { ids: [...data.updated_id], action: EVENT_BUS_ACTIONS.DELETE },
      PRODUCT_TAG_EVENT_BUS_INFO.detailType,
      PRODUCT_TAG_EVENT_BUS_INFO.source,
    );
    return data;
  }

  @Delete(':id')
  async remove(@Param('id', ParseIntPipe) id: number): Promise<string> {
    const data = await this.tagService.remove(id);
    this.externalApiHelper.syncProductAttributes(
      { ids: [id], action: EVENT_BUS_ACTIONS.DELETE },
      PRODUCT_TAG_EVENT_BUS_INFO.detailType,
      PRODUCT_TAG_EVENT_BUS_INFO.source,
    );
    return data;
  }

  @Get('list')
  async findAllTags(
    @Query(new ValidationPipe({ transform: true }))
    paginationDto: PaginationDto,
  ): Promise<{ tags: ProductsTag[]; count: number }> {
    return this.tagService.findAllTags(paginationDto);
  }

  @Get(':id/products')
  async getProductsByTag(
    @Param('id', ParseIntPipe) id: number,
    @Query(new ValidationPipe({ transform: true }))
    filterProductsDto: FilterProductsDto,
  ) {
    return this.tagService.getProductsByTag(id, filterProductsDto);
  }

  @Patch('bulk-update')
  async bulkUpdate(
    @Body(new ValidationPipe({ transform: true })) bulkUpdateDto: BulkUpdateDto,
  ) {
    const data = await this.tagService.bulkUpdate(bulkUpdateDto);
    this.externalApiHelper.syncProductAttributes(
      {
        ids: [...data.updated_id],
        status: bulkUpdateDto.status,
        action: EVENT_BUS_ACTIONS.BULK_STATUS_UPDATE,
      },
      PRODUCT_TAG_EVENT_BUS_INFO.detailType,
      PRODUCT_TAG_EVENT_BUS_INFO.source,
    );

    return data;
  }
}

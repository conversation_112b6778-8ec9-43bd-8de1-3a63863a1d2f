import {
  BadRequestException,
  Injectable,
  InternalServerErrorException,
  NotFoundException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import {
  Repository,
  In,
  Equal,
  Like,
  FindManyOptions,
  Between,
  MoreThan,
  LessThan,
} from 'typeorm';
import { ProductsTag } from 'src/database/entities/product/product-tag.entity';
import { CatalogProduct } from 'src/database/entities/product/main-product.entity';
import { CreateProductsTagDto } from './dtos/create-tag-dto';
import { PaginationDto, SortDirection } from './dtos/pagination-dto';
import { UpdateProductsDto } from './dtos/update-tag-dto';
import { FilterProductsDto } from '../catalog-product/dto/filter-product.dto';
import { CatalogProductService } from '../catalog-product/catalog-product.service';
import { BulkUpdateDto } from '../product-attachment/dtos/bulk-update.dto';
import { ProductHelperService } from '../catalog-product/product-helper.service';

@Injectable()
export class ProductsTagService {
  constructor(
    @InjectRepository(ProductsTag)
    private readonly tagRepository: Repository<ProductsTag>,
    @InjectRepository(CatalogProduct)
    private readonly productRepository: Repository<CatalogProduct>,
    private readonly catalogService: CatalogProductService,
    private readonly productHelperService: ProductHelperService,
  ) {}

  async createProductTag(
    createTagDto: CreateProductsTagDto,
  ): Promise<CreateProductsTagDto> {
    try {
      const { productIds, ...rest } = createTagDto;
      const tag = this.tagRepository.create({
        ...rest,
      });

      if (productIds && productIds.length > 0) {
        const products = await this.productRepository.findBy({
          id: In(productIds),
        });

        if (products.length !== productIds.length) {
          throw new BadRequestException(
            'One or more product IDs are incorrect',
          );
        }

        tag.products = products;
      }

      return this.tagRepository.save(tag);
    } catch (e) {
      throw new InternalServerErrorException(e?.message || e);
    }
  }

  async addProductsToTag(
    tag: ProductsTag,
    productIds: number[],
  ): Promise<ProductsTag> {
    try {
      const productsToAdd = await this.productRepository.findBy({
        id: In(productIds),
      });

      if (productsToAdd.length !== productIds.length) {
        throw new BadRequestException('One or more product IDs are incorrect');
      }
      // Filter out products that are already associated with the tag
      const newProducts = productsToAdd.filter(
        (product) =>
          !tag.products.some(
            (existingProduct) => existingProduct.id === product.id,
          ),
      );

      if (newProducts.length) {
        tag.products = [...tag.products, ...newProducts];
        return this.tagRepository.save(tag);
      }
      return tag;
    } catch (e) {
      throw e;
    }
  }

  async removeProductsFromTag(
    tag: ProductsTag,
    productIds: number[],
  ): Promise<ProductsTag> {
    tag.products = tag.products.filter(
      (product) => !productIds.includes(product.id),
    );
    return this.tagRepository.save(tag);
  }

  formatTagUpdateInfo(
    updateTagDto: Partial<CreateProductsTagDto>,
    tag: ProductsTag,
  ): ProductsTag {
    const {
      name,
      unique_code,
      value,
      tag_type,
      position,
      description,
      status,
    } = updateTagDto;
    tag.name = name ?? tag.name;
    tag.unique_code = unique_code ?? tag.unique_code;
    tag.value = value ?? tag.value;
    tag.tag_type = tag_type ?? tag.tag_type;
    tag.position = position ?? tag.position;
    tag.description = description ?? tag.description;
    tag.status = status ?? tag.status;
    return tag;
  }

  findOneTag(id: number): Promise<ProductsTag> {
    return this.tagRepository.findOne({
      where: { id: id },
      relations: ['products', 'products.catalogProductFlatRelations'],
      select: {
        products: {
          id: true,
          sku: true,
          type_id: true,
          catalogProductFlatRelations: {
            name: true,
            price: true,
            visibility: true,
            thumbnail: true,
          },
        },
      },
    });
  }

  async findAllTags(paginationDto: PaginationDto): Promise<{
    tags: ProductsTag[];
    count: number;
    page: number;
    limit: number;
  }> {
    try {
      const {
        page = 1,
        limit = 10,
        id,
        status,
        name,
        unique_code,
        tag_type,
        sort_by,
        sort_direction,
        to_created_at,
        from_created_at,
        to_updated_at,
        from_updated_at,
      } = paginationDto;

      const options: FindManyOptions<ProductsTag> = {
        skip: (page - 1) * limit,
        take: limit,
        where: {},
        order: {},
      };

      const where: any = {};
      if (id) where.id = id;
      if (unique_code) where.unique_code = unique_code;
      if (tag_type) where.tag_type = tag_type;
      if (name) where.name = Like(`%${name}%`);
      if (status !== undefined) where.status = Equal(status);
      options.where = where;

      if (sort_by) {
        options.order[sort_by] =
          sort_direction === SortDirection.ASC ? 'ASC' : 'DESC';
      }

      // Filtering for created_at
      if (from_created_at && to_created_at) {
        where.created_at = Between(from_created_at, to_created_at);
      } else {
        if (from_created_at) where.created_at = MoreThan(from_created_at);
        if (to_created_at) where.created_at = LessThan(to_created_at);
      }

      // Filtering for updated_at
      if (from_updated_at && to_updated_at) {
        where.updated_at = Between(from_updated_at, to_updated_at);
      } else {
        if (from_updated_at) where.updated_at = MoreThan(from_updated_at);
        if (to_updated_at) where.updated_at = LessThan(to_updated_at);
      }
      const [tags, total] = await this.tagRepository.findAndCount(options);
      return {
        tags,
        count: total,
        page,
        limit,
      };
    } catch (e) {
      throw new InternalServerErrorException(e?.message || e);
    }
  }

  async updateTag(
    tagId: number,
    infoUpdate: UpdateProductsDto,
    addProductIds?: number[],
    removeProductIds?: number[],
  ) {
    try {
      if (
        !addProductIds &&
        !removeProductIds &&
        Object.keys(infoUpdate).length === 0
      ) {
        throw new BadRequestException(
          'Please provide product ids or information to update. ',
        );
      }
      let tag = await this.findOneTag(tagId);

      if (!tag) {
        throw new NotFoundException('Tag not found');
      }

      if (infoUpdate) {
        tag = this.formatTagUpdateInfo(infoUpdate, tag);
        if (!addProductIds && !removeProductIds) {
          return this.tagRepository.save(tag);
        }
      }

      if (removeProductIds && removeProductIds.length) {
        tag = await this.removeProductsFromTag(tag, removeProductIds);
      }

      if (addProductIds && addProductIds?.length) {
        tag = await this.addProductsToTag(tag, addProductIds);
      }

      return tag;
    } catch (e) {
      if (e instanceof NotFoundException || e instanceof BadRequestException) {
        throw e;
      } else {
        throw new InternalServerErrorException(e || e?.message);
      }
    }
  }

  async remove(id: number): Promise<string> {
    let tag = await this.tagRepository.findOne({ where: { id } });
    if (!tag) {
      throw new NotFoundException('Tag not found');
    }
    await this.tagRepository.delete(id);
    return 'ok';
  }

  async getProductsByTag(id: number, filterProductsDto: FilterProductsDto) {
    try {
      const attachment = await this.tagRepository.findOne({
        where: { id: id },
        relations: ['products'],
        select: {
          id: true,
          products: {
            id: true,
          },
        },
      });

      if (!attachment) {
        throw new NotFoundException('Attachment not found');
      }

      const productIds = attachment.products.map((product) => product.id);

      if (productIds.length) {
        return this.productHelperService.getPaginatedProducts(
          filterProductsDto,
          productIds,
        );
      }
      const { page = 1, limit = 10 } = filterProductsDto;
      return {
        products: [],
        count: 0,
        page,
        limit,
      };
    } catch (e) {
      if (e instanceof NotFoundException || e instanceof BadRequestException) {
        throw e;
      } else {
        throw new InternalServerErrorException(e || e?.message);
      }
    }
  }

  async bulkUpdate(updateData: BulkUpdateDto) {
    try {
      const { status, ids } = updateData;
      const validRecords = await this.tagRepository.find({
        where: { id: In(ids) },
        select: ['id'],
      });
      const validIds = validRecords.map((record) => record.id);
      if (validIds.length > 0) {
        await this.tagRepository.update(
          { id: In(validIds) },
          { status: status },
        );
      }
      const invalidIds = ids.filter((id: number) => !validIds.includes(id));
      return {
        updated_id: validIds,
        invalid_id: invalidIds,
      };
    } catch (e) {
      throw new InternalServerErrorException(e || e?.message);
    }
  }

  async bulkDelete(ids: number[]) {
    try {
      const validRecords = await this.tagRepository.find({
        where: { id: In(ids) },
        select: ['id'],
      });
      const validIds = validRecords.map((record) => record.id);
      if (validIds.length > 0) {
        await this.tagRepository.delete(validIds);
      }
      const invalidIds = ids.filter((id) => !validIds.includes(id));
      return {
        updated_id: validIds,
        invalid_id: invalidIds,
      };
    } catch (e) {
      throw new InternalServerErrorException(e || e?.message);
    }
  }
}

import {
  IsOptional,
  IsInt,
  Min,
  IsDateString,
  IsBoolean,
  IsString,
  IsEnum,
  IsIn,
} from 'class-validator';
import { Transform } from 'class-transformer';
import { TagType } from 'src/database/entities/product/product-tag.entity';

export enum SortDirection {
  ASC = 'asc',
  DESC = 'desc',
}
export class PaginationDto {
  @IsOptional()
  @IsInt()
  @Min(1)
  @Transform(({ value }) => parseInt(value, 10))
  page: number = 1;

  @IsOptional()
  @IsInt()
  @Min(1)
  @Transform(({ value }) => parseInt(value, 10))
  limit: number = 10;

  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => value === 'true')
  status?: boolean;

  @IsOptional()
  @IsString()
  name?: string;

  @IsOptional()
  @IsInt()
  @Transform(({ value }) => parseInt(value, 10))
  id?: number;

  @IsOptional()
  @IsEnum(TagType)
  tag_type?: TagType;

  @IsOptional()
  @IsString()
  unique_code?: string;

  @IsOptional()
  @IsString()
  @IsIn(['id', 'created_at', 'updated_at'])
  sort_by?: string;

  @IsOptional()
  @IsEnum(SortDirection)
  sort_direction?: SortDirection = SortDirection.ASC;

  @IsOptional()
  @IsDateString()
  from_created_at?: string;

  @IsOptional()
  @IsDateString()
  to_created_at?: string;

  @IsOptional()
  @IsDateString()
  from_updated_at?: string;

  @IsOptional()
  @IsDateString()
  to_updated_at?: string;
}

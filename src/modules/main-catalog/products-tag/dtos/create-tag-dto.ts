import {
  IsBoolean,
  IsEnum,
  IsNotEmpty,
  IsOptional,
  IsString,
  IsArray,
  ArrayNotEmpty,
  ArrayMinSize,
  IsInt,
  Min,
} from 'class-validator';
import { Type } from 'class-transformer';
import {
  TagType,
  TagPosition,
} from 'src/database/entities/product/product-tag.entity';

export class CreateProductsTagDto {
  @IsString()
  @IsNotEmpty()
  name: string;

  @IsString()
  @IsNotEmpty()
  unique_code: string;

  @IsString()
  @IsNotEmpty()
  value: string;

  @IsEnum(TagType)
  @IsNotEmpty()
  tag_type: TagType;

  @IsEnum(TagPosition)
  @IsNotEmpty()
  position: TagPosition;

  @IsString()
  @IsOptional()
  description?: string;

  @IsBoolean()
  @IsOptional()
  status?: boolean;

  @IsArray()
  @ArrayNotEmpty()
  @ArrayMinSize(1)
  @IsInt({ each: true })
  @Min(1, { each: true })
  @IsOptional()
  @Type(() => Number)
  productIds?: number[];
}

import {
  <PERSON><PERSON><PERSON><PERSON>,
  IsInt,
  <PERSON>rrayNotEmpty,
  ArrayMinSize,
  IsOptional,
  IsString,
  IsEnum,
  IsBoolean,
  Min,
} from 'class-validator';
import { Type } from 'class-transformer';
import {
  TagType,
  TagPosition,
} from 'src/database/entities/product/product-tag.entity';

export class UpdateProductsDto {
  @IsArray()
  @IsOptional()
  @ArrayNotEmpty()
  @ArrayMinSize(1)
  @IsInt({ each: true })
  @Type(() => Number)
  addProductIds?: number[];

  @IsArray()
  @IsOptional()
  @ArrayNotEmpty()
  @ArrayMinSize(1)
  @IsInt({ each: true })
  @Type(() => Number)
  removeProductIds?: number[];

  @IsString()
  @IsOptional()
  name?: string;

  @IsString()
  @IsOptional()
  unique_code?: string;

  @IsString()
  @IsOptional()
  value?: string;

  @IsEnum(TagType)
  @IsOptional()
  tag_type?: TagType;

  @IsEnum(TagPosition)
  @IsOptional()
  position?: TagPosition;

  @IsString()
  @IsOptional()
  description?: string;

  @IsBoolean()
  @IsOptional()
  status?: boolean;
}

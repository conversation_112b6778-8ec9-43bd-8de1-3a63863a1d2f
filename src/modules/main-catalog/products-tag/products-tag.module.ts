import { Module } from '@nestjs/common';
import { ProductsTagController } from './products-tag.controller';
import { ProductsTagService } from './products-tag.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ProductsTag } from 'src/database/entities/product/product-tag.entity';
import { CatalogProduct } from 'src/database/entities/product/main-product.entity';
import { CatalogProductModule } from '../catalog-product/catalog-product.module';
import { SyncProductTagAttachmentMapper } from 'src/mapper/tag-attachment-sync-mapper';
import { UtilsModule } from 'src/utils/utils.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([ProductsTag, CatalogProduct]),
    CatalogProductModule,
    UtilsModule,
  ],
  controllers: [ProductsTagController],
  providers: [ProductsTagService, SyncProductTagAttachmentMapper],
})
export class ProductsTagModule {}

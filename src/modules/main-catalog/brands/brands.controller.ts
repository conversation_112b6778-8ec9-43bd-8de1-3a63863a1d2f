import {
  Controller,
  Get,
  Query,
  ParseIntPipe,
  Body,
  Post,
  Patch,
  Param,
  Delete,
  ValidationPipe,
  Headers,
} from '@nestjs/common';
import { Brand } from 'src/database/entities/brand/brand.entity';
import { BrandsService } from './brands.service';
import {
  CreateBrandDto,
  PaginationOptions,
  UpdateBrandDto,
  GetBrandsListQuery,
} from './dtos/brands.dto';

@Controller('/v1/catalog-admin/brands')
export class BrandsController {
  constructor(private readonly brandsService: BrandsService) {}

  @Post()
  async create(
    @Body() createBrandDto: CreateBrandDto,
    @Headers() headers: Record<string, string | string[]>,
  ): Promise<Brand> {
    return this.brandsService.create(createBrandDto, headers);
  }

  @Patch(':id')
  async update(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateBrandDto: UpdateBrandDto,
  ): Promise<Brand> {
    return this.brandsService.update(id, updateBrandDto);
  }

  @Delete(':id')
  async remove(@Param('id', ParseIntPipe) id: number): Promise<void> {
    return this.brandsService.remove(id);
  }

  @Get()
  async findAll(
    @Query(new ValidationPipe({ transform: true })) options: PaginationOptions,
  ): Promise<{ data: Brand[]; total: number; page: number; limit: number }> {
    return this.brandsService.findAll(options);
  }

  @Get('list')
  async websiteBrandList(@Query() query: GetBrandsListQuery) {
    return this.brandsService.getBrandsList(query.section_type);
  }

  @Get(':id')
  async findById(@Param('id', ParseIntPipe) id: number): Promise<Brand> {
    return this.brandsService.findOne(id);
  }
}

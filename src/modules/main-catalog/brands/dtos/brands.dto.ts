import { Transform } from 'class-transformer';
import {
  IsInt,
  IsString,
  IsOptional,
  IsBoolean,
  Length,
  IsEnum,
} from 'class-validator';
import { Brand } from 'src/database/entities/brand/brand.entity';

export class CreateBrandDto {
  @IsInt()
  attribute_id: number;

  @IsString()
  @Length(1, 255)
  attribute_value: string;

  @IsString()
  @IsOptional()
  @Length(1, 255)
  url: string;

  @IsOptional()
  @IsString()
  logo?: string;

  @IsOptional()
  @IsInt()
  sort_order?: number = 0;

  @IsOptional()
  @IsBoolean()
  is_active?: boolean = true;

  @IsOptional()
  @IsBoolean()
  is_international?: boolean = false;

  @IsOptional()
  @IsBoolean()
  is_featured?: boolean = false;

  @IsOptional()
  @IsBoolean()
  is_top?: boolean = false;

  @IsOptional()
  @IsBoolean()
  is_newly_added?: boolean = false;

  @IsOptional()
  @IsBoolean()
  is_dk_suggest?: boolean = false;
}

export class UpdateBrandDto {
  @IsOptional()
  @IsString()
  logo?: string;

  @IsOptional()
  @IsInt()
  sort_order?: number;

  @IsOptional()
  @IsBoolean()
  is_active?: boolean;

  @IsOptional()
  @IsBoolean()
  is_international?: boolean;

  @IsOptional()
  @IsBoolean()
  is_featured?: boolean;

  @IsOptional()
  @IsBoolean()
  is_top?: boolean;

  @IsOptional()
  @IsBoolean()
  is_newly_added?: boolean;

  @IsOptional()
  @IsBoolean()
  is_dk_suggest?: boolean;
}

export class PaginationOptions {
  @IsOptional()
  @Transform(({ value }) => value === 'true' || value === true)
  @IsBoolean()
  is_active?: boolean;

  @IsOptional()
  @Transform(({ value }) => value === 'true' || value === true)
  @IsBoolean()
  is_international?: boolean;

  @IsOptional()
  @Transform(({ value }) => value === 'true' || value === true)
  @IsBoolean()
  is_featured?: boolean;

  @IsOptional()
  @Transform(({ value }) => value === 'true' || value === true)
  @IsBoolean()
  is_top?: boolean;

  @IsOptional()
  @Transform(({ value }) => value === 'true' || value === true)
  @IsBoolean()
  is_newly_added?: boolean;

  @IsOptional()
  @Transform(({ value }) => value === 'true' || value === true)
  @IsBoolean()
  is_dk_suggest?: boolean;

  @IsOptional()
  @IsInt()
  @Transform(({ value }) => parseInt(value, 10))
  page: number = 1;

  @IsOptional()
  @Transform(({ value }) => parseInt(value, 10))
  @IsInt()
  limit: number = 10;

  @IsOptional()
  @IsString()
  @Length(1, 255)
  brand_name: string;

  @IsString()
  @IsOptional()
  @Length(1, 255)
  url: string;
}

export enum BrandListType {
  SectionBrands = 'section_brands',
  AllBrands = 'all_brands',
}

export class GetBrandsListQuery {
  @IsOptional()
  @IsEnum(BrandListType, { message: 'Invalid section type' })
  section_type: BrandListType;
}

export interface SectionedBrands {
  top: Brand[];
  featured: Brand[];
  newly_added: Brand[];
  international: Brand[];
  dk_suggest: Brand[];
}

export interface GroupedBrands {
  [key: string]: Brand[];
}

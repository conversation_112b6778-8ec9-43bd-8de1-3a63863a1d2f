import { Module, forwardRef } from '@nestjs/common';
import { BrandsController } from './brands.controller';
import { BrandsService } from './brands.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Brand } from 'src/database/entities/brand/brand.entity';
import { CatalogProductModule } from '../catalog-product/catalog-product.module';
import { CatalogCategoryModule } from '../catalog-category/catalog-category.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([Brand]),
    CatalogProductModule,
    forwardRef(() => CatalogCategoryModule),
  ],
  controllers: [BrandsController],
  providers: [BrandsService],
  exports: [BrandsService],
})
export class BrandsModule {}

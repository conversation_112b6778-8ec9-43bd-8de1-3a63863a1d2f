import {
  CreateBrandDto,
  UpdateBrandDto,
  PaginationOptions,
  BrandListType,
  SectionedBrands,
  GroupedBrands,
} from './dtos/brands.dto';
import {
  Inject,
  Injectable,
  InternalServerErrorException,
  NotFoundException,
  forwardRef,
} from '@nestjs/common';
import {
  Repository,
  FindOptionsWhere,
  EntityManager,
  QueryRunner,
  Like,
} from 'typeorm';
import { Brand } from 'src/database/entities/brand/brand.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { CatalogProductService } from '../catalog-product/catalog-product.service';
import { CatalogCategoryService } from '../catalog-category/catalog-category.service';
import env from 'src/config/env';

@Injectable()
export class BrandsService {
  constructor(
    @InjectRepository(Brand)
    private readonly brandRepository: Repository<Brand>,
    private readonly catalogProductService: CatalogProductService,
    private readonly entityManager: EntityManager,
    @Inject(forwardRef(() => CatalogCategoryService))
    private readonly catalogCategoryService: CatalogCategoryService,
  ) {}

  async create(
    createBrandDto: CreateBrandDto,
    headers: Record<string, string | string[]>,
  ): Promise<Brand> {
    const connection = this.entityManager.connection;
    const queryRunner = connection.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();
    try {
      const { attribute_id, attribute_value, ...rest } = createBrandDto;

      const { category } = await this.catalogCategoryService.createCatgory(
        {
          name: attribute_value,
          parent_id: +env.dk.dental_brands_id,
        },
        queryRunner,
        headers,
      );

      const attributeOptionsData =
        await this.catalogProductService.createAttributeOptions(
          attribute_id,
          [attribute_value],
          queryRunner,
        );

      if (!attributeOptionsData?.new_options_list?.length)
        throw new InternalServerErrorException(
          'Unable to create attribute options',
        );

      if (!category.id) {
        throw new InternalServerErrorException(
          'Unable to add brand in category',
        );
      }

      const brand = this.brandRepository.create({
        category_id: category.id,
        brand_id: attributeOptionsData.new_options_list[0].id,
        brand_name: attribute_value,
        url: category.url_key,
        ...rest,
      });

      const savedBrand = await queryRunner.manager.save(Brand, brand);
      await queryRunner.commitTransaction();

      return savedBrand;
    } catch (e) {
      await queryRunner.rollbackTransaction();
      throw new InternalServerErrorException(e?.message || e);
    } finally {
      await queryRunner.release();
    }
  }

  async findAll(
    options: PaginationOptions,
  ): Promise<{ data: Brand[]; total: number; page: number; limit: number }> {
    const {
      page = 1,
      limit = 50,
      is_active,
      is_international,
      is_featured,
      is_top,
      is_newly_added,
      is_dk_suggest,
      brand_name,
      url,
    } = options;

    const filters: FindOptionsWhere<Brand> = {};
    if (is_active !== undefined) filters.is_active = is_active;
    if (is_international !== undefined)
      filters.is_international = is_international;
    if (is_featured !== undefined) filters.is_featured = is_featured;
    if (is_top !== undefined) filters.is_top = is_top;
    if (is_newly_added !== undefined) filters.is_newly_added = is_newly_added;
    if (is_dk_suggest !== undefined) filters.is_dk_suggest = is_dk_suggest;
    if (brand_name) filters.brand_name = Like(`%${brand_name}%`);
    if (url) filters.url = Like(`%${url}%`);

    const [data, total] = await this.brandRepository.findAndCount({
      where: filters,
      order: { created_at: 'DESC' },
      skip: (page - 1) * limit,
      take: limit,
    });

    return {
      data,
      total,
      page,
      limit,
    };
  }

  async findOne(id: number): Promise<Brand> {
    const brand = await this.brandRepository.findOneBy({ id });
    if (!brand) {
      throw new NotFoundException(`Brand with ID ${id} not found`);
    }
    return brand;
  }

  async update(id: number, updateBrandDto: UpdateBrandDto): Promise<Brand> {
    const data = await this.findOne(id);
    await this.brandRepository.update(id, updateBrandDto);
    return { ...data, ...updateBrandDto };
  }

  async remove(id: number): Promise<void> {
    const brand = await this.findOne(id);
    await this.brandRepository.remove(brand);
  }

  async updateBrandUrlKey(
    categoryId: number,
    url_key: string,
    queryRunner: QueryRunner,
  ) {
    const brandExist = await this.brandRepository.findOne({
      where: {
        category_id: categoryId,
      },
    });

    if (brandExist) {
      brandExist.url = url_key;
      await queryRunner.manager.save(Brand, brandExist);
    }
  }

  async getBrandsList(section_layout: BrandListType) {
    let brands: Brand[];

    if (section_layout === BrandListType.SectionBrands) {
      brands = await this.brandRepository.find({
        where: [
          { is_active: true, is_featured: true },
          { is_active: true, is_top: true },
          { is_active: true, is_dk_suggest: true },
          { is_active: true, is_international: true },
          { is_active: true, is_newly_added: true },
        ],
      });

      return brands.reduce<SectionedBrands>(
        (acc, brand) => {
          if (brand.is_top) acc.top.push(brand);
          if (brand.is_featured) acc.featured.push(brand);
          if (brand.is_newly_added) acc.newly_added.push(brand);
          if (brand.is_international) acc.international.push(brand);
          if (brand.is_dk_suggest) acc.dk_suggest.push(brand);
          return acc;
        },
        {
          top: [],
          featured: [],
          newly_added: [],
          international: [],
          dk_suggest: [],
        },
      );
    }

    brands = await this.brandRepository.find({
      where: {
        is_active: true,
      },
    });

    const groupedBrands = brands.reduce<GroupedBrands>(
      (acc, brand) => {
        const firstLetter = brand.brand_name.charAt(0).toUpperCase();

        if (firstLetter >= 'A' && firstLetter <= 'Z') {
          acc[firstLetter] = acc[firstLetter] || [];
          acc[firstLetter].push(brand);
        } else {
          acc['#'] = acc['#'] || [];
          acc['#'].push(brand);
        }

        return acc;
      },
      { '#': [] },
    );

    const orderedGroupedBrands = { '#': groupedBrands['#'] };

    for (let i = 65; i <= 90; i++) {
      const letter = String.fromCharCode(i);
      if (groupedBrands[letter]) {
        orderedGroupedBrands[letter] = groupedBrands[letter];
      }
    }

    return { brands: orderedGroupedBrands };
  }
}

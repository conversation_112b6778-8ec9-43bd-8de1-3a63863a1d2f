import {
  Injectable,
  InternalServerErrorException,
  BadRequestException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ProductSuggestion } from 'src/database/entities/product/product-suggestion.entity';
import { LoggerService } from 'src/utils/logger-service';
import { ProductSuggestionDto } from './dto/product-suggestion.dto';
import { ExternalApiHelper } from 'src/utils/external-api-helper';
import env from 'src/config/env';

@Injectable()
export class ProductSuggestionService {
  constructor(
    @InjectRepository(ProductSuggestion)
    private readonly productSuggestionRepository: Repository<ProductSuggestion>,
    private readonly logger: LoggerService,
    private readonly externalApiHelper: ExternalApiHelper,
  ) {}

  async addProductSuggestionDetails(body: ProductSuggestionDto, headers) {
    try {
      const authToken = headers.authorization;
      let customerInfo;

      if (authToken) {
        customerInfo =
          await this.externalApiHelper.fetchCustomerInfoInfoHelper(authToken);
      }

      let { searched_key, product_name, brand, comment } = body;

      brand = brand?.trim() || null;
      comment = comment?.trim() || null;

      await this.productSuggestionRepository.save({
        searched_key,
        product_name,
        brand,
        comment,
        user: customerInfo ? customerInfo.email : null,
      });

      return { message: 'Product suggestion saved' };
    } catch (err) {
      this.logger.error('Failed to save product suggestion', err);
      if (err.status === 400) {
        throw err;
      } else {
        throw new InternalServerErrorException(
          'Failed to save product suggestion',
        );
      }
    }
  }

  async getProductSuggestionDetails(body) {
    try {
      let { pagination } = body;

      let queryConditions: any = {};

      queryConditions['skip'] =
        ((pagination?.page || 1) - 1) * env.sqlQueryResultsize;
      queryConditions['take'] = pagination?.size
        ? pagination.size
        : env.sqlQueryResultsize;

      let suggestionsData = await this.productSuggestionRepository.findAndCount(
        {
          ...queryConditions,
          order: { id: 'DESC' },
        },
      );

      let suggestionsDataArray = suggestionsData[0];
      let suggestionsDataCount = suggestionsData[1];

      let countPerPage = pagination?.page
        ? pagination.size
        : env.sqlQueryResultsize;

      return {
        item_count: suggestionsDataCount,
        pages_count: Math.ceil(suggestionsDataCount / countPerPage),
        page_no: pagination?.page,
        page_size: suggestionsDataArray.length,
        items: suggestionsDataArray,
      };
    } catch (err) {
      this.logger.error('Failed to fetch product suggestion list', err);
      if (err.status === 400) {
        throw err;
      } else {
        throw new InternalServerErrorException(
          'Failed to fetch product suggestion list',
        );
      }
    }
  }
}

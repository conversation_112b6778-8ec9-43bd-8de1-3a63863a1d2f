import { Type, Transform } from 'class-transformer';
import {
  IsArray,
  IsBoolean,
  IsIn,
  IsInt,
  IsNumber,
  IsOptional,
  IsString,
  IsNotEmpty,
  IsUrl,
  ValidateNested,
} from 'class-validator';

export class ProductSuggestionDto {
  @IsNotEmpty()
  @IsString()
  searched_key: string;

  @IsNotEmpty()
  @IsString()
  product_name: string;

  @IsOptional()
  @IsString()
  brand: string;

  @IsOptional()
  @IsString()
  comment: string;
}

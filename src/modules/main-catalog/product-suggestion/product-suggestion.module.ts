import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ProductSuggestion } from 'src/database/entities/product/product-suggestion.entity';
import { ProductSuggestionService } from './product-suggestion.service';
import { ProductSuggestionController } from './product-suggestion.controller';
import { UtilsModule } from 'src/utils/utils.module';

@Module({
  imports: [TypeOrmModule.forFeature([ProductSuggestion]), UtilsModule],
  providers: [ProductSuggestionService],
  controllers: [ProductSuggestionController],
  exports: [ProductSuggestionService],
})
export class ProductSuggestionModule {}

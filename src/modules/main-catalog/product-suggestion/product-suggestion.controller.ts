import { Controller, Body, Post, Get, Query, Headers } from '@nestjs/common';
import { ProductSuggestionService } from './product-suggestion.service';
import { ProductSuggestionDto } from './dto/product-suggestion.dto';

@Controller('v1/catalog-admin/product-suggestion')
export class ProductSuggestionController {
  constructor(
    private readonly productSuggestionService: ProductSuggestionService,
  ) {}

  @Post()
  async addProductSuggestionDetails(
    @Body() body: ProductSuggestionDto,
    @Headers()
    headers: {
      Authorization: string;
    },
  ) {
    return await this.productSuggestionService.addProductSuggestionDetails(
      body,
      headers,
    );
  }

  @Get()
  async getProductSuggestionsList(
    @Query('page') page?: number,
    @Query('size') size?: number,
  ) {
    let requestBody = {
      pagination: {
        page,
        size,
      },
    };
    return await this.productSuggestionService.getProductSuggestionDetails(
      requestBody,
    );
  }
}

import {
  IsBoolean,
  IsEnum,
  IsNotEmpty,
  IsOptional,
  IsString,
  IsArray,
  ArrayNotEmpty,
  ArrayMinSize,
  IsInt,
  Min,
  Matches,
} from 'class-validator';
import { Type } from 'class-transformer';
import { TierPriceCustomerGroupEnum } from '../../catalog-product/dto/catalog-product.dto';

export enum DeviceTypeValues {
  ALL = 'all',
  WEBSITE = 'website',
  PHONE_BROWSER = 'phone_browser',
  IPAD_BROWSER = 'ipad_browser',
  PHONE_APPLICATION = 'phone_application',
}

export class CreateAttachmentDto {
  @IsString()
  @IsOptional()
  url: string;

  @IsString()
  @IsNotEmpty()
  description: string;

  @IsString()
  @IsOptional()
  thumbnail: string;

  @IsBoolean()
  @IsOptional()
  status?: boolean;

  @IsArray()
  @ArrayNotEmpty()
  @ArrayMinSize(1)
  @IsInt({ each: true })
  @Min(1, { each: true })
  @IsOptional()
  @Type(() => Number)
  productIds?: number[];

  @IsOptional()
  @IsArray()
  @ArrayNotEmpty()
  @IsEnum(DeviceTypeValues, { each: true })
  active_device: DeviceTypeValues[];

  @IsOptional()
  @IsArray()
  @ArrayNotEmpty()
  @IsEnum(TierPriceCustomerGroupEnum, { each: true })
  customer_group: TierPriceCustomerGroupEnum[];
}

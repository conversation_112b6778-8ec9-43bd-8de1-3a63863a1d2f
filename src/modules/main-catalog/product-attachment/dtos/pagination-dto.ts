import {
  IsOptional,
  IsInt,
  Min,
  IsDateString,
  IsBoolean,
  IsString,
  IsIn,
  IsEnum,
  isString,
} from 'class-validator';
import { Transform } from 'class-transformer';

export enum SortDirection {
  ASC = 'asc',
  DESC = 'desc',
}
export class PaginationDto {
  @IsOptional()
  @IsInt()
  @Min(1)
  @Transform(({ value }) => parseInt(value, 10))
  page: number = 1;

  @IsOptional()
  @IsInt()
  @Min(1)
  @Transform(({ value }) => parseInt(value, 10))
  limit: number = 10;

  @IsOptional()
  @IsDateString()
  created_at?: string;

  @IsOptional()
  @IsDateString()
  updated_at?: string;

  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => value === 'true')
  status?: boolean;

  @IsOptional()
  @IsString()
  description?: string;

  @IsOptional()
  @IsInt()
  @Transform(({ value }) => parseInt(value, 10))
  id?: number;

  @IsOptional()
  @IsString()
  @IsIn(['id', 'created_at', 'updated_at'])
  sort_by?: string;

  @IsOptional()
  @IsEnum(SortDirection)
  sort_direction?: SortDirection = SortDirection.ASC;

  @IsOptional()
  @IsDateString()
  from_created_at?: string;

  @IsOptional()
  @IsDateString()
  to_created_at?: string;

  @IsOptional()
  @IsDateString()
  from_updated_at?: string;

  @IsOptional()
  @IsDateString()
  to_updated_at?: string;
}

import {
  IsArray,
  IsInt,
  ArrayNotEmpty,
  ArrayMinSize,
  IsOptional,
  IsString,
  IsBoolean,
  IsEnum,
} from 'class-validator';
import { Type } from 'class-transformer';
import { DeviceTypeValues } from './create-attachment-dto';
import { TierPriceCustomerGroupEnum } from '../../catalog-product/dto/catalog-product.dto';

export class UpdateProductsDto {
  @IsArray()
  @IsOptional()
  @ArrayNotEmpty()
  @ArrayMinSize(1)
  @IsInt({ each: true })
  @Type(() => Number)
  addProductIds?: number[];

  @IsArray()
  @IsOptional()
  @ArrayNotEmpty()
  @ArrayMinSize(1)
  @IsInt({ each: true })
  @Type(() => Number)
  removeProductIds?: number[];

  @IsString()
  @IsOptional()
  description?: string;

  @IsBoolean()
  @IsOptional()
  status?: boolean;

  @IsString()
  @IsOptional()
  url?: string;

  @IsOptional()
  @IsString()
  thumbnail?: string;

  @IsOptional()
  @IsArray()
  @ArrayNotEmpty()
  @IsEnum(DeviceTypeValues, { each: true })
  active_device: DeviceTypeValues[];

  @IsOptional()
  @IsArray()
  @ArrayNotEmpty()
  @IsEnum(TierPriceCustomerGroupEnum, { each: true })
  customer_group: TierPriceCustomerGroupEnum[];
}

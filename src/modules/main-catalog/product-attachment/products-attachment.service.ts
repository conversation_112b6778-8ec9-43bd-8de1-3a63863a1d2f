import {
  BadRequestException,
  Injectable,
  InternalServerErrorException,
  NotFoundException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import {
  Repository,
  In,
  Like,
  Equal,
  FindManyOptions,
  Between,
  MoreThan,
  LessThan,
} from 'typeorm';
import { ProductsAttachment } from 'src/database/entities/product/product-attachment.entity';
import { CatalogProduct } from 'src/database/entities/product/main-product.entity';
import { CreateAttachmentDto } from './dtos/create-attachment-dto';
import { PaginationDto, SortDirection } from './dtos/pagination-dto';
import { UpdateProductsDto } from './dtos/update-attachment-dto';
import { CatalogProductService } from '../catalog-product/catalog-product.service';
import { FilterProductsDto } from '../catalog-product/dto/filter-product.dto';
import { BulkUpdateDto } from './dtos/bulk-update.dto';
import { ProductHelperService } from '../catalog-product/product-helper.service';

@Injectable()
export class ProductsAttachmentService {
  constructor(
    @InjectRepository(ProductsAttachment)
    private readonly attachmentRepository: Repository<ProductsAttachment>,
    @InjectRepository(CatalogProduct)
    private readonly productRepository: Repository<CatalogProduct>,
    private readonly catalogService: CatalogProductService,
    private readonly productHelperService: ProductHelperService,
  ) {}

  async createAttachment(
    createAttachmentDto: CreateAttachmentDto, // : Promise<CreateAttachmentDto>
  ) {
    try {
      const { productIds, active_device, customer_group, ...rest } =
        createAttachmentDto;
      const attachment = this.attachmentRepository.create({
        active_device: active_device?.join(','),
        customer_group: customer_group?.join(','),
        ...rest,
      });

      if (productIds && productIds.length > 0) {
        const products = await this.productRepository.findBy({
          id: In(productIds),
        });

        if (products.length !== productIds.length) {
          throw new BadRequestException(
            'One or more product IDs are incorrect',
          );
        }

        attachment.products = products;
      }

      return this.attachmentRepository.save(attachment);
    } catch (e) {
      throw new InternalServerErrorException(e?.message || e);
    }
  }

  async addProductsToAttachment(
    attachment: ProductsAttachment,
    productIds: number[],
  ): Promise<ProductsAttachment> {
    try {
      const productsToAdd = await this.productRepository.findBy({
        id: In(productIds),
      });

      if (productsToAdd.length !== productIds.length) {
        throw new BadRequestException('One or more product IDs are incorrect');
      }
      // Filter out products that are already associated with the tag
      const newProducts = productsToAdd.filter(
        (product) =>
          !attachment.products.some(
            (existingProduct) => existingProduct.id === product.id,
          ),
      );
      if (newProducts.length) {
        attachment.products = [...attachment.products, ...newProducts];
        return this.attachmentRepository.save(attachment);
      }
      return attachment;
    } catch (e) {
      throw e;
    }
  }

  async removeProductsFromAttachment(
    attachment: ProductsAttachment,
    productIds: number[],
  ): Promise<ProductsAttachment> {
    attachment.products = attachment.products.filter(
      (product) => !productIds.includes(product.id),
    );
    return this.attachmentRepository.save(attachment);
  }

  formatAttachmentUpdateInfo(
    updateAttachmentDto: Partial<CreateAttachmentDto>,
    attachment: ProductsAttachment,
  ): ProductsAttachment {
    const {
      url,
      thumbnail,
      description,
      status,
      active_device,
      customer_group,
    } = updateAttachmentDto;
    attachment.url = url ?? attachment.url;
    attachment.thumbnail = thumbnail ?? attachment.thumbnail;
    attachment.description = description ?? attachment.description;
    attachment.status = status ?? attachment.status;
    attachment.active_device = active_device
      ? active_device.join(',')
      : attachment.active_device;
    attachment.customer_group = customer_group
      ? customer_group.join(',')
      : attachment.customer_group;

    return attachment;
  }

  findOneAttachment(id: number): Promise<ProductsAttachment> {
    return this.attachmentRepository.findOne({
      where: { id: id },
      relations: ['products', 'products.catalogProductFlatRelations'],
      select: {
        products: {
          id: true,
          sku: true,
          type_id: true,
          catalogProductFlatRelations: {
            name: true,
            price: true,
            visibility: true,
            thumbnail: true,
          },
        },
      },
    });
  }

  async findAllAttachments(paginationDto: PaginationDto): Promise<{
    attachments: ProductsAttachment[];
    count: number;
    page: number;
  }> {
    try {
      const {
        page,
        limit,
        id,
        status,
        description,
        sort_by,
        sort_direction,
        from_created_at,
        from_updated_at,
        to_updated_at,
        to_created_at,
      } = paginationDto;

      const options: FindManyOptions<ProductsAttachment> = {
        skip: (page - 1) * limit,
        take: limit,
        where: {},
        select: {
          id: true,
          created_at: true,
          updated_at: true,
          status: true,
          description: true,
          thumbnail: true,
          url: true,
          active_device: true,
          customer_group: true,
        },
        order: {},
      };

      const where: any = {};

      if (id) where.id = id;
      if (status !== undefined) where.status = Equal(status);
      if (description) where.description = Like(`%${description}%`);

      if (from_created_at && to_created_at) {
        where.created_at = Between(from_created_at, to_created_at);
      } else {
        if (from_created_at) where.created_at = MoreThan(from_created_at);
        if (to_created_at) where.created_at = LessThan(to_created_at);
      }

      // Filtering for updated_at
      if (from_updated_at && to_updated_at) {
        where.updated_at = Between(from_updated_at, to_updated_at);
      } else {
        if (from_updated_at) where.updated_at = MoreThan(from_updated_at);
        if (to_updated_at) where.updated_at = LessThan(to_updated_at);
      }

      options.where = where;

      if (sort_by) {
        options.order[sort_by] =
          sort_direction === SortDirection.ASC ? 'ASC' : 'DESC';
      }

      const [attachments, total] =
        await this.attachmentRepository.findAndCount(options);

      return {
        attachments,
        count: total,
        page,
      };
    } catch (e) {
      throw new InternalServerErrorException(e?.message || e);
    }
  }

  async updateAttachment(
    id: number,
    infoUpdate: UpdateProductsDto,
    addProductIds?: number[],
    removeProductIds?: number[],
  ) {
    try {
      if (
        !addProductIds &&
        !removeProductIds &&
        Object.keys(infoUpdate).length === 0
      ) {
        throw new BadRequestException(
          'Please provide product ids or information to update. ',
        );
      }
      let attachment = await this.findOneAttachment(id);

      if (!attachment) {
        throw new NotFoundException('Attachment not found');
      }

      if (infoUpdate) {
        attachment = this.formatAttachmentUpdateInfo(infoUpdate, attachment);
        if (!addProductIds && !removeProductIds) {
          return this.attachmentRepository.save(attachment);
        }
      }

      if (removeProductIds && removeProductIds.length) {
        attachment = await this.removeProductsFromAttachment(
          attachment,
          removeProductIds,
        );
      }

      if (addProductIds && addProductIds?.length) {
        attachment = await this.addProductsToAttachment(
          attachment,
          addProductIds,
        );
      }

      return attachment;
    } catch (e) {
      if (e instanceof NotFoundException || e instanceof BadRequestException) {
        throw e;
      } else {
        throw new InternalServerErrorException(e || e?.message);
      }
    }
  }

  async remove(id: number): Promise<string> {
    let tag = await this.attachmentRepository.findOne({ where: { id } });
    if (!tag) {
      throw new NotFoundException('Tag not found');
    }
    await this.attachmentRepository.delete(id);
    return 'ok';
  }

  async getProductsByAttachment(
    id: number,
    filterProductsDto: FilterProductsDto,
  ) {
    try {
      const attachment = await this.attachmentRepository.findOne({
        where: { id: id },
        relations: ['products'],
        select: {
          id: true,
          products: {
            id: true,
          },
        },
      });

      if (!attachment) {
        throw new NotFoundException('Attachment not found');
      }

      const productIds = attachment.products.map((product) => product.id);

      if (productIds.length) {
        return this.productHelperService.getPaginatedProducts(
          filterProductsDto,
          productIds,
        );
      }
      const { page = 1, limit = 10 } = filterProductsDto;
      return {
        products: [],
        count: 0,
        page,
        limit,
      };
    } catch (e) {
      if (e instanceof NotFoundException || e instanceof BadRequestException) {
        throw e;
      } else {
        throw new InternalServerErrorException(e || e?.message);
      }
    }
  }

  async bulkUpdate(updateData: BulkUpdateDto) {
    try {
      const { status, ids } = updateData;
      const validRecords = await this.attachmentRepository.find({
        where: { id: In(ids) },
        select: ['id'],
      });
      const validIds = validRecords.map((record) => record.id);
      if (validIds.length > 0) {
        await this.attachmentRepository.update(
          { id: In(validIds) },
          { status: status },
        );
      }
      const invalidIds = ids.filter((id: number) => !validIds.includes(id));
      return {
        updated_id: validIds,
        invalid_id: invalidIds,
      };
    } catch (e) {
      throw new InternalServerErrorException(e || e?.message);
    }
  }

  async bulkDelete(ids: number[]) {
    try {
      const validRecords = await this.attachmentRepository.find({
        where: { id: In(ids) },
        select: ['id'],
      });
      const validIds = validRecords.map((record) => record.id);
      if (validIds.length > 0) {
        await this.attachmentRepository.delete(validIds);
      }
      const invalidIds = ids.filter((id) => !validIds.includes(id));
      return {
        updated_id: validIds,
        invalid_id: invalidIds,
      };
    } catch (e) {
      throw new InternalServerErrorException(e || e?.message);
    }
  }
}

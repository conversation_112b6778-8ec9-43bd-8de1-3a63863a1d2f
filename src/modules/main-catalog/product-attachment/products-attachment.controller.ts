import {
  Controller,
  Post,
  Body,
  Param,
  ValidationPipe,
  Get,
  Query,
  ParseIntPipe,
  Delete,
  Patch,
} from '@nestjs/common';
import { ProductsAttachmentService } from './products-attachment.service';
import { CreateAttachmentDto } from './dtos/create-attachment-dto';
import { UpdateProductsDto } from './dtos/update-attachment-dto';
import { PaginationDto } from './dtos/pagination-dto';
import { ProductsAttachment } from 'src/database/entities/product/product-attachment.entity';
import { FilterProductsDto } from '../catalog-product/dto/filter-product.dto';
import { BulkUpdateDto } from './dtos/bulk-update.dto';
import { BulkDeleteDto } from './dtos/bulk-delete.dto';
import { ExternalApiHelper } from 'src/utils/external-api-helper';
import {
  PRODUCT_ATTACHMENT_EVENT_BUS_INFO,
  EVENT_BUS_ACTIONS,
} from 'src/config/constants';
import { SyncProductTagAttachmentMapper } from 'src/mapper/tag-attachment-sync-mapper';
@Controller('/v1/catalog-admin/attachments')
export class ProductsAttachmentController {
  constructor(
    private readonly attachmentService: ProductsAttachmentService,
    private readonly externalApiHelper: ExternalApiHelper,
    private readonly syncProductTagAttachmentMapper: SyncProductTagAttachmentMapper,
  ) {}

  @Post()
  async create(
    @Body(new ValidationPipe({ transform: true }))
    attachmentDto: CreateAttachmentDto, // : Promise<CreateAttachmentDto>
  ) {
    const data = await this.attachmentService.createAttachment(attachmentDto);
    const mappedData =
      this.syncProductTagAttachmentMapper.syncProductAttachmentsMapper(
        data,
        EVENT_BUS_ACTIONS.CREATE,
      );
    this.externalApiHelper.syncProductAttributes(
      mappedData,
      PRODUCT_ATTACHMENT_EVENT_BUS_INFO.detailType,
      PRODUCT_ATTACHMENT_EVENT_BUS_INFO.source,
    );
    return data;
  }

  @Post(':id/products')
  async update(
    @Param('id', ParseIntPipe) id: number,
    @Body(new ValidationPipe({ transform: true }))
    productUpdateIds: UpdateProductsDto,
  ): Promise<ProductsAttachment> {
    const { addProductIds, removeProductIds, ...rest } = productUpdateIds;
    const data = await this.attachmentService.updateAttachment(
      id,
      rest,
      addProductIds,
      removeProductIds,
    );
    const mappedData =
      this.syncProductTagAttachmentMapper.syncProductAttachmentsMapper(
        data,
        EVENT_BUS_ACTIONS.UPDATE,
      );
    this.externalApiHelper.syncProductAttributes(
      mappedData,
      PRODUCT_ATTACHMENT_EVENT_BUS_INFO.detailType,
      PRODUCT_ATTACHMENT_EVENT_BUS_INFO.source,
    );
    return data;
  }

  @Delete('bulk-delete')
  async bulkDelete(
    @Body(new ValidationPipe({ transform: true })) bulkDeleteDto: BulkDeleteDto,
  ) {
    const { ids } = bulkDeleteDto;
    const data = await this.attachmentService.bulkDelete(ids);
    this.externalApiHelper.syncProductAttributes(
      { ids: [...data.updated_id], action: EVENT_BUS_ACTIONS.DELETE },
      PRODUCT_ATTACHMENT_EVENT_BUS_INFO.detailType,
      PRODUCT_ATTACHMENT_EVENT_BUS_INFO.source,
    );
    return data;
  }

  @Delete(':id')
  async remove(@Param('id', ParseIntPipe) id: number): Promise<string> {
    const data = await this.attachmentService.remove(id);
    this.externalApiHelper.syncProductAttributes(
      { ids: [id], action: EVENT_BUS_ACTIONS.DELETE },
      PRODUCT_ATTACHMENT_EVENT_BUS_INFO.detailType,
      PRODUCT_ATTACHMENT_EVENT_BUS_INFO.source,
    );
    return data;
  }

  @Get('list')
  async findAllAttachments(
    @Query(new ValidationPipe({ transform: true }))
    paginationDto: PaginationDto,
  ): Promise<{
    attachments: ProductsAttachment[];
    count: number;
    page: number;
  }> {
    return this.attachmentService.findAllAttachments(paginationDto);
  }

  @Get(':id/products')
  async getProductsByAttachment(
    @Param('id', ParseIntPipe) id: number,
    @Query(new ValidationPipe({ transform: true }))
    filterProductsDto: FilterProductsDto,
  ) {
    return this.attachmentService.getProductsByAttachment(
      id,
      filterProductsDto,
    );
  }

  @Patch('bulk-update')
  async bulkUpdate(
    @Body(new ValidationPipe({ transform: true })) bulkUpdateDto: BulkUpdateDto,
  ) {
    const data = await this.attachmentService.bulkUpdate(bulkUpdateDto);

    this.externalApiHelper.syncProductAttributes(
      {
        ids: [...data.updated_id],
        status: bulkUpdateDto.status,
        action: EVENT_BUS_ACTIONS.BULK_STATUS_UPDATE,
      },
      PRODUCT_ATTACHMENT_EVENT_BUS_INFO.detailType,
      PRODUCT_ATTACHMENT_EVENT_BUS_INFO.source,
    );

    return data;
  }
}

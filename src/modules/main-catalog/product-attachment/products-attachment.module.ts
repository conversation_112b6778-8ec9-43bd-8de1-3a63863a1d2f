import { Module } from '@nestjs/common';
import { ProductsAttachmentController } from './products-attachment.controller';
import { ProductsAttachmentService } from './products-attachment.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ProductsAttachment } from 'src/database/entities/product/product-attachment.entity';
import { CatalogProduct } from 'src/database/entities/product/main-product.entity';
import { CatalogProductModule } from '../catalog-product/catalog-product.module';
import { UtilsModule } from 'src/utils/utils.module';
import { SyncProductTagAttachmentMapper } from 'src/mapper/tag-attachment-sync-mapper';

@Module({
  imports: [
    TypeOrmModule.forFeature([ProductsAttachment, CatalogProduct]),
    CatalogProductModule,
    UtilsModule,
  ],
  controllers: [ProductsAttachmentController],
  providers: [ProductsAttachmentService, SyncProductTagAttachmentMapper],
})
export class ProductsAttachmentModule {}

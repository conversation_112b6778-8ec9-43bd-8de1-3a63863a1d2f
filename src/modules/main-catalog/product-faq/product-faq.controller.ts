import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Put,
  Delete,
  UseGuards,
  Query,
  ValidationPipe,
  ParseIntPipe,
} from '@nestjs/common';
import { CreateQuestionAnswerDto } from './dtos/create-question.dto';
import { UpdateQuestionAnswerDto } from './dtos/update-question.dto';
import { ProductFaqService } from './product-faq.service';
import { QuestionAnswer } from 'src/database/entities/product/product-question-answer.entity';
import { LikeDislike } from 'src/database/entities/product/likes-dislikes.entity';
import { LikeDislikeDto } from './dtos/like-dislike-dto';
import { AuthGuard } from 'src/guards/authorize-user';
import { Customer, CustomerInfo } from 'src/decorator/customer.decorator';
import { GetQuestionAnswersDto } from './dtos/get-question.dto';

@Controller('/v1/catalog-admin/questions-answers')
export class ProductFaqController {
  constructor(private readonly productFaqService: ProductFaqService) { }

  @Post()
  @UseGuards(AuthGuard)
  create(
    @Body(new ValidationPipe({ transform: true }))
    createQuestionAnswerDto: CreateQuestionAnswerDto,
    @Customer() customer: CustomerInfo,
  ): Promise<QuestionAnswer> {
    return this.productFaqService.create(
      createQuestionAnswerDto,
      customer?.id,
      customer?.email,
    );
  }

  @Get()
  async getQuestions(
    @Query(new ValidationPipe({ transform: true }))
    query: GetQuestionAnswersDto,
  ) {
    return this.productFaqService.getQuestionAnswers(query);
  }

  @Get(':id')
  findOne(@Param('id', ParseIntPipe) id: number): Promise<QuestionAnswer> {
    return this.productFaqService.findOne(id);
  }

  @Put(':id')
  update(
    @Param('id', ParseIntPipe) id: number,
    @Body(new ValidationPipe({ transform: true })) updateQuestionAnswerDto: UpdateQuestionAnswerDto,
  ): Promise<QuestionAnswer> {
    return this.productFaqService.update(id, updateQuestionAnswerDto);
  }

  @Delete(':id')
  remove(@Param('id', ParseIntPipe) id: number): Promise<string> {
    return this.productFaqService.remove(id);
  }

  @Post('likes/action')
  @UseGuards(AuthGuard)
  async handlelikeDislikeAction(
    @Body(new ValidationPipe({ transform: true }))
    likeDislikeDto: LikeDislikeDto,
    @Customer() customer: CustomerInfo,
  ): Promise<LikeDislike> {
    return this.productFaqService.handleLikeDislikeAction(
      likeDislikeDto,
      customer.id,
    );
  }

  @Get('products/:id')
  async getProductWithQuestionsAndLikes(
    @Param('id', ParseIntPipe) id: number,
    @Query(new ValidationPipe({ transform: true })) query: GetQuestionAnswersDto,
  ) {
    return this.productFaqService.findProductWithQuestionsAndLikes(id, query);
  }
}

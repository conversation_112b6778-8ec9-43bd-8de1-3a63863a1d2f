export interface QuestionsInputs {
  question: string;
  product_id: number;
  like?: number;
  dislike?: number;
  enable: boolean;
  product_image?: string;
  product_name?: string;
  status?: string;
  customer_token?: string;
  user?: string;
  answer?: AnswerDataInput;
}

export interface AnswerDataInput {
  value?: string;
  updated_by?: string;
  updated_at?: string;
}

export interface SearchInput {
  search: string;
  product_id?: number;
  enable?: string;
}

export interface QuestionsPagination {
  rowsPerPage: number;
  pageNumber: number;
}

export interface QuestionsOutput {
  result: QuestionsOutputData[];
  count: number;
}

export interface QuestionsOutputData {
  _id: string;
  question: string;
  answer: AnswerData;
  product_id: number;
  enable: boolean;
  product_image?: string;
  product_name?: string;
  like: number;
  dislike: number;
  created_at: string;
  status: string;
  customer_token: string;
  user: string;
}

export interface AnswerData {
  value: String;
  updated_by: string;
  updated_at: string;
}

export interface ProductQnaList {
  id: number;
  product_id: number;
  question: string;
  answer: string;
  status: boolean;
  admin_email: string;
  customer_email: string;
  updated_at: Date;
  created_at: Date;
  like_count: number;
  dislike_count: number;
  is_like?: boolean | null;
}

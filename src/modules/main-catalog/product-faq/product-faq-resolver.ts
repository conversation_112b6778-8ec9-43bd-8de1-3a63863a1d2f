import { Resolver, Query, Args, Mutation, Context } from '@nestjs/graphql';
import { AuthGuard } from 'src/guards/authorize-user';
import { UseGuards, BadRequestException } from '@nestjs/common';
import {
  QuestionsInputs,
  SearchInput,
  QuestionsPagination,
  QuestionsOutput,
  QuestionsOutputData,
} from './interface/graphql-request';
import { ProductFaqService } from './product-faq.service';
import { ProductQnaMapper } from 'src/mapper/product-qna-mapper';
import { GetQuestionAnswersDto } from './dtos/get-question.dto';
import { CreateQuestionAnswerDto } from './dtos/create-question.dto';
import { Customer, CustomerInfo } from 'src/decorator/customer.decorator';
import { LikeDislike } from 'src/database/entities/product/likes-dislikes.entity';
import { LikeDislikeDto } from './dtos/like-dislike-dto';
import { ExternalApiHelper } from 'src/utils/external-api-helper';

@Resolver()
export class QuestionsAnswersResolver {
  constructor(
    private readonly productFaqService: ProductFaqService,
    private readonly productQnaMapper: ProductQnaMapper,
    private readonly externalApiHelper: ExternalApiHelper,
  ) {}

  @Query('getQuestions')
  async getQuestions(
    @Args('search') searchInput: SearchInput,
    @Args('pagination') pagination: QuestionsPagination,
    @Context() context: any,
  ): Promise<Partial<QuestionsOutput>> {
    const requestArgs: GetQuestionAnswersDto = {
      limit: pagination.rowsPerPage || 50,
      page: +pagination.pageNumber || 1,
      status: true,
    };
    if (searchInput.product_id) {
      requestArgs.product_id = +searchInput.product_id;
    }
    if (searchInput.search) {
      requestArgs.search = searchInput.search;
    }
    if (searchInput.enable) {
      requestArgs.status = searchInput.enable === 'Enabled' ? true : false;
    }
    const authToken = this.externalApiHelper.getUserToken(
      context.req.headers['authorization'],
    );

    if (authToken) {
      const customerExists =
        await this.externalApiHelper.getCustomerDetails(authToken);
      if (!customerExists) throw new BadRequestException('Invalid auth token');
      const response = await this.productFaqService.getQuestionAnswers(
        requestArgs,
        +customerExists.id,
      );
      return {
        result: this.productQnaMapper.getGqlQuestionAnswersList(response.data),
        count: response.total,
      };
    }

    const response =
      await this.productFaqService.getQuestionAnswers(requestArgs);
    return {
      result: this.productQnaMapper.getGqlQuestionAnswersList(response.data),
      count: response.total,
    };
  }

  @Mutation('addQuestionsAnswer')
  @UseGuards(AuthGuard)
  async addQuestionsAnswer(
    @Args('input') input: QuestionsInputs,
    @Customer() customer: CustomerInfo,
  ): Promise<Partial<QuestionsOutputData>> {
    const reqArgs: CreateQuestionAnswerDto = {
      product_id: +input.product_id,
      question_text: input.question,
    };
    const data = await this.productFaqService.create(
      reqArgs,
      customer?.id,
      customer?.email,
    );
    return this.productQnaMapper.createQuestionGqlResponse(data);
  }

  @Mutation('productQnaLikeDislike')
  @UseGuards(AuthGuard)
  async handleLikeDislikeAction(
    @Args('input') likeDislikeDto: LikeDislikeDto,
    @Customer() customer: CustomerInfo,
  ) {
    return this.productFaqService.handleLikeDislikeAction(
      likeDislikeDto,
      customer.id,
    );
  }
}

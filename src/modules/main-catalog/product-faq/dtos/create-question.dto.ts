import {
  IsNotEmpty,
  IsString,
  IsBoolean,
  IsOptional,
  IsInt,
} from 'class-validator';

export class CreateQuestionAnswerDto {
  @IsInt()
  @IsNotEmpty()
  product_id: number;

  @IsOptional()
  @IsString()
  admin_email?: string;

  @IsString()
  @IsNotEmpty()
  question_text: string;

  @IsString()
  @IsOptional()
  answer_text?: string;

  @IsBoolean()
  @IsOptional()
  status?: boolean;
}

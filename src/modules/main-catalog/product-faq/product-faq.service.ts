import {
  Injectable,
  InternalServerErrorException,
  NotFoundException,
} from '@nestjs/common';
import { QuestionAnswer } from 'src/database/entities/product/product-question-answer.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Equal, Like, FindManyOptions } from 'typeorm';
import { CreateQuestionAnswerDto } from './dtos/create-question.dto';
import { UpdateQuestionAnswerDto } from './dtos/update-question.dto';
import { CatalogProduct } from 'src/database/entities/product/main-product.entity';
import { LikeDislike } from 'src/database/entities/product/likes-dislikes.entity';
import { LikeDislikeDto, ActionType } from './dtos/like-dislike-dto';
import { GetQuestionAnswersDto } from './dtos/get-question.dto';
import env from 'src/config/env';

@Injectable()
export class ProductFaqService {
  constructor(
    @InjectRepository(LikeDislike)
    private readonly likeDislikeRepository: Repository<LikeDislike>,
    @InjectRepository(QuestionAnswer)
    private readonly questionAnswerRepository: Repository<QuestionAnswer>,
    @InjectRepository(CatalogProduct)
    private readonly catalogProductRepository: Repository<CatalogProduct>,
  ) {}

  async create(
    createQuestionAnswerDto: CreateQuestionAnswerDto,
    customerId?: number,
    customer_email?: string,
  ): Promise<QuestionAnswer> {
    try {
      const { product_id, ...rest } = createQuestionAnswerDto;
      const product = await this.catalogProductRepository.findOneBy({
        id: product_id,
      });
      if (!product) {
        throw new NotFoundException(`Product with id ${product_id} not found`);
      }
      const questionAnswer = this.questionAnswerRepository.create({
        ...rest,
        customer_id: customerId,
        customer_email,
        product,
      });
      return this.questionAnswerRepository.save(questionAnswer);
    } catch (e) {
      if (e instanceof NotFoundException) {
        throw e;
      } else {
        throw new InternalServerErrorException(e?.message || e);
      }
    }
  }

  async findAll(): Promise<QuestionAnswer[]> {
    return this.questionAnswerRepository.find();
  }

  async findOne(id: number): Promise<QuestionAnswer> {
    return this.questionAnswerRepository.findOneBy({ id });
  }

  async update(
    id: number,
    updateQuestionAnswerDto: UpdateQuestionAnswerDto,
  ): Promise<QuestionAnswer> {
    try {
      const existingQuestionAnswer =
        await this.questionAnswerRepository.findOneBy({
          id,
        });

      if (!existingQuestionAnswer) {
        throw new NotFoundException(`QuestionAnswer with ID ${id} not found`);
      }

      const updatedQuestionAnswer = Object.assign(
        existingQuestionAnswer,
        updateQuestionAnswerDto,
      );

      return this.questionAnswerRepository.save(updatedQuestionAnswer);
    } catch (e) {
      if (e instanceof NotFoundException) {
        throw e;
      } else {
        throw new InternalServerErrorException(e?.message || e);
      }
    }
  }

  async remove(id: number): Promise<string> {
    const existingQuestionAnswer =
      await this.questionAnswerRepository.findOneBy({ id });
    if (!existingQuestionAnswer) {
      throw new NotFoundException(`QuestionAnswer with ID ${id} not found`);
    }
    await this.likeDislikeRepository.delete({ question_id: id });
    await this.questionAnswerRepository.delete(id);
    return 'ok';
  }

  async handleLikeDislikeAction(
    likeDislikeDto: LikeDislikeDto,
    customerId: number,
  ) {
    try {
      const { question_id, action } = likeDislikeDto;

      const question = await this.questionAnswerRepository.findOneBy({
        id: question_id,
      });

      if (!question) {
        throw new NotFoundException(
          `QuestionAnswer with ID ${question_id} not found`,
        );
      }

      let likeDislike = await this.likeDislikeRepository.findOne({
        where: { customer_id: customerId, question_id: question_id },
      });

      if (action === ActionType.REMOVE) {
        if (!likeDislike) {
          throw new NotFoundException(
            `LikeDislike record not found for customer ID ${customerId} and question ID ${question_id}`,
          );
        }
        await this.likeDislikeRepository.remove(likeDislike);
        const [likeCount, dislikeCount] = await Promise.all([
          this.getActionCount(question_id, true),
          this.getActionCount(question_id, false),
        ]);
        likeDislike.is_like = null;
        return {
          ...likeDislike,
          like_count: likeCount,
          dislike_count: dislikeCount,
        };
      }

      if (likeDislike) {
        if (likeDislike.is_like === (action === ActionType.LIKE)) {
          const [likeCount, dislikeCount] = await Promise.all([
            this.getActionCount(question_id, true),
            this.getActionCount(question_id, false),
          ]);
          return {
            ...likeDislike,
            like_count: likeCount,
            dislike_count: dislikeCount,
          };
        }
        likeDislike.is_like = action === ActionType.LIKE;
        likeDislike.updated_at = new Date();
      } else {
        likeDislike = this.likeDislikeRepository.create({
          customer_id: customerId,
          is_like: action === ActionType.LIKE, // true for LIKE, false for DISLIKE
          question,
        });
      }

      likeDislike = await this.likeDislikeRepository.save(likeDislike);
      const [likeCount, dislikeCount] = await Promise.all([
        this.getActionCount(question_id, true),
        this.getActionCount(question_id, false),
      ]);
      return {
        ...likeDislike,
        like_count: likeCount,
        dislike_count: dislikeCount,
      };
    } catch (e) {
      if (e instanceof NotFoundException) {
        throw e;
      } else {
        throw new InternalServerErrorException(e?.message || e);
      }
    }
  }

  async getActionCount(question_id: number, action: boolean) {
    const count = await this.likeDislikeRepository.count({
      where: { question_id, is_like: action },
    });
    return count;
  }

  async findProductWithQuestionsAndLikes(
    productId: number,
    queryParams: GetQuestionAnswersDto,
  ) {
    try {
      const product = await this.catalogProductRepository.findOne({
        where: {
          id: productId,
        },
        relations: ['catalogProductFlatRelations'],
        select: {
          id: true,
          sku: true,
          type_id: true,
          catalogProductFlatRelations: {
            name: true,
            price: true,
            visibility: true,
            thumbnail: true,
          },
        },
      });

      if (!product) {
        throw new NotFoundException(`Product with ID ${productId} not found`);
      }

      if (
        product.catalogProductFlatRelations &&
        product.catalogProductFlatRelations.thumbnail
      ) {
        product.catalogProductFlatRelations.thumbnail = this.transformThumbnail(
          product.catalogProductFlatRelations.thumbnail,
        );
      }

      queryParams.product_id = productId;
      const { data, total } = await this.getFilteredQuestions(queryParams);
      const questionAnswers = this.mapQuestionData(data);
      return {
        productId,
        data: questionAnswers,
        total,
        page: queryParams.page || 1,
        limit: queryParams.limit || 50,
        product,
      };
    } catch (e) {
      if (e instanceof NotFoundException) {
        throw new NotFoundException(e.message);
      } else {
        throw new InternalServerErrorException(e?.message);
      }
    }
  }

  private transformThumbnail(thumbnail: string): string {
    if (thumbnail.startsWith('s3/')) {
      return `${env.aws.bucketBaseUrl}${thumbnail.replace(/^s3\//, '')}`;
    } else if (!thumbnail.startsWith('http')) {
      return `${thumbnail}`;
    }
    return thumbnail; // Return as is if it doesn't match any conditions
  }

  async getFilteredQuestions(
    queryParams: GetQuestionAnswersDto,
    isProdRelation?: boolean,
  ): Promise<{ data: QuestionAnswer[]; total: number }> {
    try {
      const {
        page = 1,
        limit = 50,
        id,
        status,
        product_id,
        search,
        product_name,
      } = queryParams;

      const options: FindManyOptions<QuestionAnswer> = {
        skip: (page - 1) * limit,
        take: limit,
        relations: ['likes_dislikes'],
        where: {},
        order: { id: 'DESC' },
      };

      if (isProdRelation) {
        options.relations = ['likes_dislikes', 'likes_dislikes'];
        options.select = {
          product: {
            id: true,
            catalogProductFlatRelations: {
              name: true,
            },
          },
        };
      }

      if (id) {
        options.where = { ...options.where, id };
      }

      if (status !== undefined) {
        options.where = { ...options.where, status: Equal(status) };
      }

      if (product_id) {
        options.where = { ...options.where, product_id };
      }

      if (product_name) {
        options.where = {
          ...options.where,
          product: {
            catalogProductFlatRelations: { name: Like(`%${product_name}%`) },
          },
        };
      }

      if (search) {
        options.where = {
          ...options.where,
          question_text: Like(`%${search}%`),
        };
      }

      const [results, total] =
        await this.questionAnswerRepository.findAndCount(options);

      return { data: results, total };
    } catch (e) {
      if (e instanceof NotFoundException) {
        throw e;
      } else {
        throw new InternalServerErrorException(e?.message || e);
      }
    }
  }

  likeDislikeCount(likeDislike: LikeDislike[]) {
    return likeDislike.reduce((counts, likeDislike) => {
      const questionId = likeDislike.question_id;
      if (!counts[questionId]) {
        counts[questionId] = { likeCount: 0, dislikeCount: 0 };
      }
      if (likeDislike.is_like) {
        counts[questionId].likeCount += 1;
      } else {
        counts[questionId].dislikeCount += 1;
      }

      return counts;
    }, {});
  }

  mapQuestionData(questionAnswers: QuestionAnswer[], customerId?: number) {
    return (
      questionAnswers?.map((question: QuestionAnswer) => {
        const count =
          question?.likes_dislikes?.length > 0
            ? this.likeDislikeCount(question.likes_dislikes)
            : {};
        let is_like = null;

        if (customerId) {
          is_like =
            question?.likes_dislikes?.find(
              (likeDislike) => likeDislike.customer_id === customerId,
            )?.is_like ?? null;
        }

        return {
          id: question.id,
          product_id: question.product_id,
          question: question.question_text,
          answer: question.answer_text,
          status: question.status,
          admin_email: question.admin_email,
          customer_email: question.customer_email,
          updated_at: question.updated_at,
          created_at: question.created_at,
          like_count: count?.[question.id]?.likeCount ?? 0,
          dislike_count: count?.[question.id]?.dislikeCount ?? 0,
          is_like,
        };
      }) ?? []
    );
  }

  async getQuestionAnswers(query: GetQuestionAnswersDto, customerId?: number) {
    const { data, total } = await this.getFilteredQuestions(query, true);
    const res = this.mapQuestionData(data, customerId);
    return {
      data: res,
      total,
      page: query.page || 1,
      limit: query.limit || 50,
    };
  }
}

import { Modu<PERSON> } from '@nestjs/common';
import { ProductFaqService } from './product-faq.service';
import { ProductFaqController } from './product-faq.controller';
import { CatalogProduct } from 'src/database/entities/product/main-product.entity';
import { LikeDislike } from 'src/database/entities/product/likes-dislikes.entity';
import { QuestionAnswer } from 'src/database/entities/product/product-question-answer.entity';
import { TypeOrmModule } from '@nestjs/typeorm';
import { UtilsModule } from 'src/utils/utils.module';
import { ProductQnaMapper } from 'src/mapper/product-qna-mapper';
import { QuestionsAnswersResolver } from './product-faq-resolver';

@Module({
  imports: [
    TypeOrmModule.forFeature([LikeDislike, QuestionAnswer, CatalogProduct]),
    UtilsModule,
  ],
  providers: [ProductFaqService, ProductQnaMapper, QuestionsAnswersResolver],
  controllers: [ProductFaqController],
})
export class ProductFaqModule {}

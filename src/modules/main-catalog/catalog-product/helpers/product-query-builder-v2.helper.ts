// import { FindManyOptions } from 'typeorm';
// import { Between, LessThanOrEqual, MoreThanOrEqual, Like, In } from 'typeorm';
// import { CatalogProduct } from 'src/database/entities/product/main-product.entity';
// import { ListProductsV2Dto, SortByFieldEnum } from '../dto/list-products-v2.dto';

// export class ProductQueryBuilderV2Helper {
//   /**
//    * Builds a unified TypeORM query options object for product listing
//    */
//   static buildQueryOptions(
//     dto: ListProductsV2Dto,
//     maxValues?: {
//       maxId?: number;
//       maxPrice?: number;
//       maxQty?: number;
//       maxMsrp?: number;
//       maxExpiryDate?: string;
//     }
//   ): FindManyOptions<CatalogProduct> {
//     const options: FindManyOptions<CatalogProduct> = {
//       relations: this.buildRelations(dto),
//       where: {},
//       select: this.buildSelectClause(dto),
//       skip: (dto.page - 1) * dto.size,
//       take: dto.size,
//       order: this.buildOrderClause(dto),
//     };

//     // Build where conditions
//     const whereConditions = this.buildWhereConditions(dto, maxValues);
//     if (Object.keys(whereConditions).length > 0) {
//       options.where = whereConditions;
//     }

//     return options;
//   }

//   /**
//    * Builds relations array based on what data is requested
//    */
//   private static buildRelations(dto: ListProductsV2Dto): string[] {
//     const relations: string[] = ['catalogProductFlatRelations'];

//     if (dto.include_inventory) {
//       relations.push('inventoryAttributesRelations');
//     }

//     if (dto.include_categories) {
//       relations.push('productCategoryRelations.category');
//     }

//     if (dto.include_media) {
//       relations.push('productImageRelation');
//       relations.push('productVideoRelation');
//     }

//     return relations;
//   }

//   /**
//    * Builds select clause for optimized field selection
//    */
//   private static buildSelectClause(dto: ListProductsV2Dto): any {
//     const selectClause: any = {
//       id: true,
//       sku: true,
//       status: true,
//       type_id: true,
//       created_at: true,
//       updated_at: true,
//       catalogProductFlatRelations: {
//         id: true,
//         name: true,
//         price: true,
//         special_price: true,
//         weight: true,
//         manufacturer: true,
//         visibility: true,
//         thumbnail: true,
//         is_cod: true,
//         hsn_code: true,
//         tax_class_id: true,
//         reward_point_product: true,
//         pd_expiry_date: true,
//         gtin: true,
//         demo_available: true,
//         url_key: true,
//         international_active: true,
//         msrp: true,
//       },
//     };

//     // Add description fields only if search is being performed
//     if (dto.search_keyword) {
//       selectClause.catalogProductFlatRelations.description = true;
//       selectClause.catalogProductFlatRelations.short_description = true;
//       selectClause.catalogProductFlatRelations.packaging = true;
//       selectClause.catalogProductFlatRelations.warranty = true;
//       selectClause.catalogProductFlatRelations.key_specifications = true;
//       selectClause.catalogProductFlatRelations.features = true;
//     }

//     if (dto.include_inventory) {
//       selectClause.inventoryAttributesRelations = {
//         id: true,
//         is_in_stock: true,
//         qty: true,
//         min_sale_qty: true,
//         max_sale_qty: true,
//         backorders: true,
//       };
//     }

//     if (dto.include_categories) {
//       selectClause.productCategoryRelations = {
//         id: true,
//         position: true,
//         category: {
//           id: true,
//           name: true,
//           status: true,
//           parent_id: true,
//         },
//       };
//     }

//     if (dto.include_media) {
//       selectClause.productImageRelation = {
//         id: true,
//         value: true,
//         image_tags: true,
//       };
//       selectClause.productVideoRelation = {
//         id: true,
//       };
//     }

//     return selectClause;
//   }

//   /**
//    * Builds comprehensive where conditions combining all filters
//    */
//   private static buildWhereConditions(
//     dto: ListProductsV2Dto,
//     maxValues?: {
//       maxId?: number;
//       maxPrice?: number;
//       maxQty?: number;
//       maxMsrp?: number;
//       maxExpiryDate?: string;
//     }
//   ): any {
//     const whereConditions: any = {};

//     // Main product filters
//     this.addMainProductFilters(whereConditions, dto, maxValues);

//     // Flat table filters
//     this.addFlatTableFilters(whereConditions, dto, maxValues);

//     // Inventory filters
//     this.addInventoryFilters(whereConditions, dto, maxValues);

//     // Search filters
//     this.addSearchFilters(whereConditions, dto);

//     return whereConditions;
//   }

//   /**
//    * Adds filters for main product table
//    */
//   private static addMainProductFilters(
//     whereConditions: any,
//     dto: ListProductsV2Dto,
//     maxValues?: any
//   ): void {
//     // ID range filters
//     if (dto.id_from !== undefined && dto.id_to !== undefined) {
//       whereConditions.id = Between(dto.id_from, dto.id_to);
//     } else if (dto.id_to !== undefined) {
//       whereConditions.id = LessThanOrEqual(dto.id_to);
//     } else if (dto.id_from !== undefined) {
//       const maxId = maxValues?.maxId || 999999999;
//       whereConditions.id = Between(dto.id_from, maxId);
//     }

//     // Status filter
//     if (dto.status !== undefined) {
//       whereConditions.status = dto.status;
//     }

//     // Type filter
//     if (dto.type_id !== undefined) {
//       whereConditions.type_id = dto.type_id;
//     }
//   }

//   /**
//    * Adds filters for catalog_product_flat table
//    */
//   private static addFlatTableFilters(
//     whereConditions: any,
//     dto: ListProductsV2Dto,
//     maxValues?: any
//   ): void {
//     const flatConditions: any = {};

//     // String filters - exact match for now, can be enhanced to LIKE
//     if (dto.name !== undefined) {
//       flatConditions.name = Like(`%${dto.name}%`);
//     }

//     if (dto.sku !== undefined) {
//       flatConditions.sku = Like(`%${dto.sku}%`);
//     }

//     if (dto.url_key !== undefined) {
//       flatConditions.url_key = dto.url_key;
//     }

//     if (dto.gstin !== undefined) {
//       flatConditions.gtin = dto.gstin;
//     }

//     // Price range filters
//     if (dto.price_from !== undefined && dto.price_to !== undefined) {
//       flatConditions.price = Between(dto.price_from, dto.price_to);
//     } else if (dto.price_to !== undefined) {
//       flatConditions.price = LessThanOrEqual(dto.price_to);
//     } else if (dto.price_from !== undefined) {
//       const maxPrice = maxValues?.maxPrice || 999999999;
//       flatConditions.price = Between(dto.price_from, maxPrice);
//     }

//     // MSRP range filters
//     if (dto.msrp_from !== undefined && dto.msrp_to !== undefined) {
//       flatConditions.msrp = Between(dto.msrp_from, dto.msrp_to);
//     } else if (dto.msrp_to !== undefined) {
//       flatConditions.msrp = LessThanOrEqual(dto.msrp_to);
//     } else if (dto.msrp_from !== undefined) {
//       const maxMsrp = maxValues?.maxMsrp || 999999999;
//       flatConditions.msrp = Between(dto.msrp_from, maxMsrp);
//     }

//     // Product expiry date filters
//     if (dto.product_expiry_from !== undefined && dto.product_expiry_to !== undefined) {
//       flatConditions.pd_expiry_date = Between(dto.product_expiry_from, dto.product_expiry_to);
//     } else if (dto.product_expiry_to !== undefined) {
//       flatConditions.pd_expiry_date = LessThanOrEqual(dto.product_expiry_to);
//     } else if (dto.product_expiry_from !== undefined) {
//       const maxExpiryDate = maxValues?.maxExpiryDate || '2099-12-31';
//       flatConditions.pd_expiry_date = Between(dto.product_expiry_from, maxExpiryDate);
//     }

//     // Boolean filters
//     if (dto.demo_available !== undefined) {
//       flatConditions.demo_available = dto.demo_available;
//     }

//     // Select filters
//     if (dto.visibility !== undefined) {
//       flatConditions.visibility = dto.visibility;
//     }

//     if (dto.manufacturer !== undefined) {
//       flatConditions.manufacturer = dto.manufacturer;
//     }

//     // Add flat conditions to main where clause
//     if (Object.keys(flatConditions).length > 0) {
//       whereConditions.catalogProductFlatRelations = flatConditions;
//     }
//   }

//   /**
//    * Adds filters for inventory attributes
//    */
//   private static addInventoryFilters(
//     whereConditions: any,
//     dto: ListProductsV2Dto,
//     maxValues?: any
//   ): void {
//     const inventoryConditions: any = {};

//     // Quantity range filters
//     if (dto.min_qty !== undefined && dto.max_qty !== undefined) {
//       inventoryConditions.qty = Between(dto.min_qty, dto.max_qty);
//     } else if (dto.max_qty !== undefined) {
//       inventoryConditions.qty = LessThanOrEqual(dto.max_qty);
//     } else if (dto.min_qty !== undefined) {
//       const maxQty = maxValues?.maxQty || 999999999;
//       inventoryConditions.qty = Between(dto.min_qty, maxQty);
//     }

//     // Boolean filters
//     if (dto.backorders !== undefined) {
//       inventoryConditions.backorders = dto.backorders;
//     }

//     if (dto.is_in_stock !== undefined) {
//       inventoryConditions.is_in_stock = dto.is_in_stock;
//     }

//     // Add inventory conditions to main where clause
//     if (Object.keys(inventoryConditions).length > 0) {
//       whereConditions.inventoryAttributesRelations = inventoryConditions;
//     }
//   }

//   /**
//    * Adds search filters across multiple fields
//    */
//   private static addSearchFilters(whereConditions: any, dto: ListProductsV2Dto): void {
//     if (!dto.search_keyword || !dto.search_fields?.length) {
//       return;
//     }

//     // For now, we'll implement a simple approach
//     // In a production environment, you might want to use a more sophisticated search
//     // like full-text search or Elasticsearch
    
//     const searchConditions: any = {};
//     const keyword = `%${dto.search_keyword}%`;

//     // Build OR conditions for search fields
//     if (dto.search_fields.includes('name' as any)) {
//       searchConditions.name = Like(keyword);
//     }
//     if (dto.search_fields.includes('sku' as any)) {
//       searchConditions.sku = Like(keyword);
//     }
//     if (dto.search_fields.includes('description' as any)) {
//       searchConditions.description = Like(keyword);
//     }
//     if (dto.search_fields.includes('short_description' as any)) {
//       searchConditions.short_description = Like(keyword);
//     }
//     if (dto.search_fields.includes('url_key' as any)) {
//       searchConditions.url_key = Like(keyword);
//     }

//     // Note: TypeORM doesn't support OR conditions easily in this format
//     // For a production implementation, you'd want to use QueryBuilder for OR conditions
//     // For now, we'll search in the name field as primary
//     if (Object.keys(searchConditions).length > 0) {
//       if (!whereConditions.catalogProductFlatRelations) {
//         whereConditions.catalogProductFlatRelations = {};
//       }
//       // Use name as primary search field for simplicity
//       whereConditions.catalogProductFlatRelations.name = Like(keyword);
//     }
//   }

//   /**
//    * Builds order clause based on sort parameters
//    */
//   private static buildOrderClause(dto: ListProductsV2Dto): any {
//     const order: any = {};

//     switch (dto.sort_by) {
//       case SortByFieldEnum.ID:
//         order.id = dto.sort_order;
//         break;
//       case SortByFieldEnum.NAME:
//         order.catalogProductFlatRelations = { name: dto.sort_order };
//         break;
//       case SortByFieldEnum.SKU:
//         order.sku = dto.sort_order;
//         break;
//       case SortByFieldEnum.PRICE:
//         order.catalogProductFlatRelations = { price: dto.sort_order };
//         break;
//       case SortByFieldEnum.CREATED_AT:
//         order.created_at = dto.sort_order;
//         break;
//       case SortByFieldEnum.UPDATED_AT:
//         order.updated_at = dto.sort_order;
//         break;
//       case SortByFieldEnum.STATUS:
//         order.status = dto.sort_order;
//         break;
//       case SortByFieldEnum.TYPE_ID:
//         order.type_id = dto.sort_order;
//         break;
//       case SortByFieldEnum.QTY:
//         order.inventoryAttributesRelations = { qty: dto.sort_order };
//         break;
//       case SortByFieldEnum.IS_IN_STOCK:
//         order.inventoryAttributesRelations = { is_in_stock: dto.sort_order };
//         break;
//       default:
//         order.id = dto.sort_order;
//     }

//     return order;
//   }
// }

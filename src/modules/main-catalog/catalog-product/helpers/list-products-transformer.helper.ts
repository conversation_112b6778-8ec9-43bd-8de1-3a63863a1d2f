import { ListProductsQueryDto, ListProductsDto } from '../dto/list-products.dto';

/**
 * Helper class to transform query parameters into the request body format
 * This handles all the edge cases and parameter transformations
 */
export class ListProductsTransformerHelper {
  /**
   * Transforms query parameters into the request body format expected by the service
   */
  static transformQueryToRequestBody(queryDto: ListProductsQueryDto): ListProductsDto {
    const requestBody: ListProductsDto = {
      filters: {},
      pagination: {
        page: queryDto.page || 1,
        size: queryDto.size || 10,
      },
    };

    // Handle search_by_keyword
    if (queryDto.search_by_keyword_field && queryDto.search_by_keyword_value) {
      // Validate field value
      if (queryDto.search_by_keyword_field === 'name' || queryDto.search_by_keyword_field === 'sku' || queryDto.search_by_keyword_field === 'url_key') {
        requestBody.search_by_keyword = {
          field: queryDto.search_by_keyword_field as 'name' | 'sku' | 'url_key',
          value: queryDto.search_by_keyword_value,
        };
      }
    }

    // Handle sort_by
    if (queryDto.sort_by_field !== undefined && queryDto.sort_by_order !== undefined) {
      // Normalize sort order to uppercase
      const normalizedOrder = queryDto.sort_by_order.toUpperCase();

      // Validate sort order
      if (normalizedOrder === 'ASC' || normalizedOrder === 'DESC') {
        requestBody.sort_by = {
          order: normalizedOrder as 'ASC' | 'DESC',
          field: queryDto.sort_by_field,
        };
      }
    }

    // Handle product_id
    if (queryDto.product_id !== undefined) {
      requestBody.product_id = queryDto.product_id;
    }

    // Handle string filters
    if (queryDto.name !== undefined) {
      requestBody.filters.name = queryDto.name;
    }

    if (queryDto.sku !== undefined) {
      requestBody.filters.sku = queryDto.sku;
    }

    if (queryDto.url_key !== undefined) {
      requestBody.filters.url_key = queryDto.url_key;
    }

    if (queryDto.gstin !== undefined) {
      requestBody.filters.gstin = queryDto.gstin;
    }

    if (queryDto.type_id !== undefined) {
      requestBody.filters.type_id = queryDto.type_id;
    }

    // Handle boolean filters
    if (queryDto.status !== undefined) {
      requestBody.filters.status = queryDto.status;
    }

    if (queryDto.backorders !== undefined) {
      requestBody.filters.backorders = queryDto.backorders;
    }

    if (queryDto.demo_available !== undefined) {
      requestBody.filters.demo_available = queryDto.demo_available;
    }

    if (queryDto.is_in_stock !== undefined) {
      requestBody.filters.is_in_stock = queryDto.is_in_stock;
    }

    // Handle number filters
    if (queryDto.visibility !== undefined) {
      requestBody.filters.visibility = queryDto.visibility;
    }

    if (queryDto.manufacturer !== undefined) {
      requestBody.filters.manufacturer = queryDto.manufacturer;
    }

    // Handle range filters - ID
    if (queryDto.id_from !== undefined) {
      requestBody.filters.id_from = queryDto.id_from;
    }

    if (queryDto.id_to !== undefined) {
      requestBody.filters.id_to = queryDto.id_to;
    }

    // Handle range filters - Price (convert string to number with validation)
    if (queryDto.price_from !== undefined) {
      const priceFrom = this.safeParseFloat(queryDto.price_from);
      if (priceFrom !== undefined) {
        requestBody.filters.price_from = priceFrom;
      }
    }

    if (queryDto.price_to !== undefined) {
      const priceTo = this.safeParseFloat(queryDto.price_to);
      if (priceTo !== undefined) {
        requestBody.filters.price_to = priceTo;
      }
    }

    // Handle range filters - Product Expiry (keep as strings)
    if (queryDto.product_expiry_from !== undefined) {
      requestBody.filters.product_expiry_from = queryDto.product_expiry_from;
    }

    if (queryDto.product_expiry_to !== undefined) {
      requestBody.filters.product_expiry_to = queryDto.product_expiry_to;
    }

    // Handle range filters - Quantity
    if (queryDto.min_qty !== undefined) {
      requestBody.filters.min_qty = queryDto.min_qty;
    }

    if (queryDto.max_qty !== undefined) {
      requestBody.filters.max_qty = queryDto.max_qty;
    }
    if (queryDto.quantity !== undefined) {
      requestBody.filters.quantity = queryDto.quantity;
    }

    // Handle range filters - MSRP
    if (queryDto.msrp_from !== undefined) {
      requestBody.filters.msrp_from = queryDto.msrp_from;
    }

    if (queryDto.msrp_to !== undefined) {
      requestBody.filters.msrp_to = queryDto.msrp_to;
    }

    return requestBody;
  }

  /**
   * Safely parses a string or number to float
   * Returns undefined if parsing fails
   */
  private static safeParseFloat(value: string | number | undefined): number | undefined {
    if (value === undefined || value === '') return undefined;
    
    if (typeof value === 'number') {
      return isNaN(value) ? undefined : value;
    }
    
    if (typeof value === 'string') {
      const parsed = parseFloat(value);
      return isNaN(parsed) ? undefined : parsed;
    }
    
    return undefined;
  }

  /**
   * Safely parses a string or number to integer
   * Returns undefined if parsing fails
   */
  private static safeParseInt(value: string | number | undefined): number | undefined {
    if (value === undefined || value === '') return undefined;
    
    if (typeof value === 'number') {
      return isNaN(value) ? undefined : Math.floor(value);
    }
    
    if (typeof value === 'string') {
      const parsed = parseInt(value, 10);
      return isNaN(parsed) ? undefined : parsed;
    }
    
    return undefined;
  }

  /**
   * Safely parses a string to boolean
   * Returns undefined if value is undefined
   */
  private static safeParseBoolean(value: string | boolean | undefined): boolean | undefined {
    if (value === undefined) return undefined;
    
    if (typeof value === 'boolean') {
      return value;
    }
    
    if (typeof value === 'string') {
      return value.toLowerCase() === 'true';
    }
    
    return undefined;
  }

  /**
   * Validates the transformed request body
   * Checks for common edge cases and inconsistencies
   */
  static validateRequestBody(requestBody: ListProductsDto): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    // Validate pagination
    if (requestBody.pagination) {
      if (requestBody.pagination.page !== undefined && requestBody.pagination.page < 1) {
        errors.push('Pagination page must be greater than 0');
      }
      
      if (requestBody.pagination.size !== undefined && (requestBody.pagination.size < 1 || requestBody.pagination.size > 100)) {
        errors.push('Pagination size must be between 1 and 100');
      }
    }

    // Validate range filters
    if (requestBody.filters) {
      // ID range validation
      if (requestBody.filters.id_from !== undefined && requestBody.filters.id_to !== undefined) {
        if (requestBody.filters.id_from > requestBody.filters.id_to) {
          errors.push('id_from must be less than or equal to id_to');
        }
      }

      // Price range validation
      if (requestBody.filters.price_from !== undefined && requestBody.filters.price_to !== undefined) {
        if (requestBody.filters.price_from > requestBody.filters.price_to) {
          errors.push('price_from must be less than or equal to price_to');
        }
      }

      // Quantity range validation
      if (requestBody.filters.min_qty !== undefined && requestBody.filters.max_qty !== undefined) {
        if (requestBody.filters.min_qty > requestBody.filters.max_qty) {
          errors.push('min_qty must be less than or equal to max_qty');
        }
      }

      // MSRP range validation
      if (requestBody.filters.msrp_from !== undefined && requestBody.filters.msrp_to !== undefined) {
        if (requestBody.filters.msrp_from > requestBody.filters.msrp_to) {
          errors.push('msrp_from must be less than or equal to msrp_to');
        }
      }

      // Date range validation (basic format check)
      if (requestBody.filters.product_expiry_from !== undefined) {
        if (!this.isValidDateString(requestBody.filters.product_expiry_from)) {
          errors.push('product_expiry_from must be a valid date string (YYYY-MM-DD format recommended)');
        }
      }

      if (requestBody.filters.product_expiry_to !== undefined) {
        if (!this.isValidDateString(requestBody.filters.product_expiry_to)) {
          errors.push('product_expiry_to must be a valid date string (YYYY-MM-DD format recommended)');
        }
      }
    }

    // Validate search_by_keyword
    if (requestBody.search_by_keyword) {
      if (!requestBody.search_by_keyword.field || !requestBody.search_by_keyword.value) {
        errors.push('search_by_keyword requires both field and value');
      }
      
      if (requestBody.search_by_keyword.field && 
          requestBody.search_by_keyword.field !== 'name' && 
          requestBody.search_by_keyword.field !== 'sku' && requestBody.search_by_keyword.field !== 'url_key') {
        errors.push('search_by_keyword.field must be either "name" or "sku" or "url_key"');
      }
    }

    // Validate sort_by
    if (requestBody.sort_by) {
      if (requestBody.sort_by.order !== 'ASC' && requestBody.sort_by.order !== 'DESC') {
        errors.push('sort_by.order must be "ASC" or "DESC"');
      }

      if (requestBody.sort_by.field !== undefined && requestBody.sort_by.field.trim() === '') {
        errors.push('sort_by.field must be a non-empty string');
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  /**
   * Basic date string validation
   */
  private static isValidDateString(dateString: string): boolean {
    if (!dateString) return false;
    
    // Try to parse the date
    const date = new Date(dateString);
    return !isNaN(date.getTime());
  }

  /**
   * Logs the transformation for debugging purposes
   */
  static logTransformation(queryDto: ListProductsQueryDto, requestBody: ListProductsDto): void {
    console.log('=== List Products Transformation ===');
    console.log('Query Parameters:', JSON.stringify(queryDto, null, 2));
    console.log('Request Body:', JSON.stringify(requestBody, null, 2));
    console.log('=====================================');
  }
}

// import { Injectable } from '@nestjs/common';
// import { InjectRepository } from '@nestjs/typeorm';
// import { Repository, In } from 'typeorm';
// import { ProductAttributesOptions } from 'src/database/entities/product/attribute-options.entity';
// import { QuestionAnswer } from 'src/database/entities/product/product-question-answer.entity';
// import { ListProductsV2Dto } from '../dto/list-products-v2.dto';

// @Injectable()
// export class ProductResponseMapperV2Helper {
//   constructor(
//     @InjectRepository(ProductAttributesOptions)
//     private readonly productAttributeOptionsRepository: Repository<ProductAttributesOptions>,
//     @InjectRepository(QuestionAnswer)
//     private readonly productFaqRepository: Repository<QuestionAnswer>,
//   ) {}

//   /**
//    * Maps raw product data to the V2 API response format
//    */
//   async mapProductsToResponse(
//     products: any[],
//     dto: ListProductsV2Dto,
//     totalCount: number
//   ): Promise<any> {
//     if (!products || products.length === 0) {
//       return {
//         item_count: totalCount,
//         pages_count: 0,
//         page_no: dto.page,
//         page_size: 0,
//         items: [],
//       };
//     }

//     // Get option value mappings for select fields
//     const optionValueMap = await this.getOptionValueMappings(products);

//     // Get FAQ status if completion percentage is requested
//     let faqStatusMap: Map<number, boolean> | null = null;
//     if (dto.include_completion_percentage) {
//       faqStatusMap = await this.getFaqStatusMap(products);
//     }

//     // Map each product
//     const mappedProducts = await Promise.all(
//       products.map(product => this.mapSingleProduct(product, dto, optionValueMap, faqStatusMap))
//     );

//     const pagesCount = Math.ceil(totalCount / dto.size);

//     return {
//       item_count: totalCount,
//       pages_count: pagesCount,
//       page_no: dto.page,
//       page_size: mappedProducts.length,
//       items: mappedProducts,
//     };
//   }

//   /**
//    * Maps a single product to the response format
//    */
//   private async mapSingleProduct(
//     product: any,
//     dto: ListProductsV2Dto,
//     optionValueMap: Map<number, string>,
//     faqStatusMap?: Map<number, boolean> | null
//   ): Promise<any> {
//     const mappedProduct: any = {
//       id: product.id,
//       sku: product.sku,
//       status: product.status,
//       type_id: product.type_id,
//       created_at: product.created_at,
//       updated_at: product.updated_at,
//     };

//     // Add attributes list (always included)
//     if (product.catalogProductFlatRelations) {
//       mappedProduct.attributes_list = this.mapAttributesList(
//         product.catalogProductFlatRelations,
//         optionValueMap
//       );
//     }

//     // Add inventory details if requested
//     if (dto.include_inventory && product.inventoryAttributesRelations) {
//       mappedProduct.inventory_details = {
//         qty: product.inventoryAttributesRelations.qty,
//         is_in_stock: product.inventoryAttributesRelations.is_in_stock,
//         min_sale_qty: product.inventoryAttributesRelations.min_sale_qty,
//         max_sale_qty: product.inventoryAttributesRelations.max_sale_qty,
//         backorders: product.inventoryAttributesRelations.backorders,
//       };
//     }

//     // Add category associations if requested
//     if (dto.include_categories && product.productCategoryRelations) {
//       mappedProduct.category_associated = product.productCategoryRelations.map(
//         (relation: any) => relation.category?.name || relation.category?.id
//       );
//     }

//     // Add media gallery if requested
//     if (dto.include_media) {
//       mappedProduct.media_gallery_entries = this.mapMediaGallery(
//         product.productImageRelation || [],
//         product.productVideoRelation || []
//       );
//     }

//     // Add completion percentage if requested
//     if (dto.include_completion_percentage && faqStatusMap) {
//       mappedProduct.completion_percentage = await this.calculateCompletionPercentage(
//         product,
//         faqStatusMap
//       );
//     }

//     return mappedProduct;
//   }

//   /**
//    * Maps attributes list with option value resolution
//    */
//   private mapAttributesList(flatRelations: any, optionValueMap: Map<number, string>): any {
//     const attributes = { ...flatRelations };

//     // Resolve option values for select/dropdown fields
//     if (attributes.manufacturer && optionValueMap.has(attributes.manufacturer)) {
//       attributes.manufacturer = optionValueMap.get(attributes.manufacturer);
//     }

//     if (attributes.visibility && optionValueMap.has(attributes.visibility)) {
//       attributes.visibility = optionValueMap.get(attributes.visibility);
//     }

//     if (attributes.tax_class_id && optionValueMap.has(attributes.tax_class_id)) {
//       attributes.tax_class_id = optionValueMap.get(attributes.tax_class_id);
//     }

//     if (attributes.dispatch_days && optionValueMap.has(attributes.dispatch_days)) {
//       attributes.dispatch_days = optionValueMap.get(attributes.dispatch_days);
//     }

//     if (attributes.country_of_manufacture && optionValueMap.has(Number(attributes.country_of_manufacture))) {
//       attributes.country_of_manufacture = optionValueMap.get(Number(attributes.country_of_manufacture));
//     }

//     return attributes;
//   }

//   /**
//    * Maps media gallery entries with proper URL formatting
//    */
//   private mapMediaGallery(imageRelations: any[], videoRelations: any[]): any[] {
//     const mediaEntries: any[] = [];

//     // Process images
//     if (imageRelations && imageRelations.length > 0) {
//       imageRelations.forEach(image => {
//         let imageUrl = image.value;
        
//         // Format image URLs based on storage location
//         if (imageUrl?.startsWith('s3/')) {
//           imageUrl = imageUrl.replace(/^s3\//, '');
//           imageUrl = `${process.env.AWS_BUCKET_BASE_URL || ''}${imageUrl}`;
//         } else if (!imageUrl?.startsWith('http')) {
//           imageUrl = `${process.env.MAGENTO_BASE_IMAGES_URL || ''}/${imageUrl}`;
//         }

//         mediaEntries.push({
//           id: image.id,
//           type: 'image',
//           value: imageUrl,
//           image_tags: image.image_tags,
//         });
//       });
//     }

//     // Process videos (basic info only for performance)
//     if (videoRelations && videoRelations.length > 0) {
//       videoRelations.forEach(video => {
//         mediaEntries.push({
//           id: video.id,
//           type: 'video',
//         });
//       });
//     }

//     return mediaEntries;
//   }

//   /**
//    * Gets option value mappings for select/dropdown fields
//    */
//   private async getOptionValueMappings(products: any[]): Promise<Map<number, string>> {
//     const optionIds: number[] = [];

//     // Collect all option IDs from products
//     products.forEach(product => {
//       const flat = product.catalogProductFlatRelations;
//       if (flat) {
//         if (flat.manufacturer) optionIds.push(flat.manufacturer);
//         if (flat.visibility) optionIds.push(flat.visibility);
//         if (flat.tax_class_id) optionIds.push(flat.tax_class_id);
//         if (flat.dispatch_days) optionIds.push(flat.dispatch_days);
//         if (flat.country_of_manufacture) optionIds.push(Number(flat.country_of_manufacture));
//       }
//     });

//     // Remove duplicates and invalid values
//     const uniqueOptionIds = [...new Set(optionIds)].filter(id => id && !isNaN(id));

//     if (uniqueOptionIds.length === 0) {
//       return new Map();
//     }

//     // Fetch option values
//     const options = await this.productAttributeOptionsRepository.find({
//       where: { id: In(uniqueOptionIds) },
//       select: ['id', 'value'],
//     });

//     // Create mapping
//     const optionValueMap = new Map<number, string>();
//     options.forEach(option => {
//       optionValueMap.set(option.id, option.value);
//     });

//     return optionValueMap;
//   }

//   /**
//    * Gets FAQ status map for completion percentage calculation
//    */
//   private async getFaqStatusMap(products: any[]): Promise<Map<number, boolean>> {
//     const productIds = products.map(p => p.id);

//     const faqs = await this.productFaqRepository.find({
//       relations: ['product'],
//       where: { product: { id: In(productIds) } },
//       select: ['product_id'],
//     });

//     const faqStatusMap = new Map<number, boolean>();
    
//     // Initialize all products as having no FAQ
//     productIds.forEach(id => faqStatusMap.set(id, false));
    
//     // Mark products with FAQs
//     faqs.forEach(faq => {
//       faqStatusMap.set(faq.product_id, true);
//     });

//     return faqStatusMap;
//   }

//   /**
//    * Calculates completion percentage for a product
//    * This is a simplified version - you can enhance based on business requirements
//    */
//   private async calculateCompletionPercentage(
//     product: any,
//     faqStatusMap: Map<number, boolean>
//   ): Promise<number> {
//     let completionScore = 0;
//     let totalFields = 0;

//     const flat = product.catalogProductFlatRelations;
//     if (!flat) return 0;

//     // Check required fields (adjust weights based on business importance)
//     const fieldChecks = [
//       { field: flat.name, weight: 20 },
//       { field: flat.description, weight: 15 },
//       { field: flat.short_description, weight: 10 },
//       { field: flat.price, weight: 20 },
//       { field: flat.thumbnail, weight: 10 },
//       { field: flat.manufacturer, weight: 5 },
//       { field: flat.hsn_code, weight: 5 },
//       { field: flat.weight, weight: 5 },
//       { field: faqStatusMap.get(product.id), weight: 10 },
//     ];

//     fieldChecks.forEach(check => {
//       totalFields += check.weight;
//       if (check.field) {
//         completionScore += check.weight;
//       }
//     });

//     return Math.round((completionScore / totalFields) * 100);
//   }
// }

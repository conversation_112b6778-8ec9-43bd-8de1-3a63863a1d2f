import { BadRequestException, Injectable, InternalServerErrorException } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { CatalogProduct } from "src/database/entities/product/main-product.entity";
import { ProductStockAlert } from "src/database/entities/product/product-stock-alert.entity";
import { UrlRewrites } from "src/database/entities/product/url-rewrites.entity";
import { dateInBasicFormat, formatDateString } from "src/utils/curentDateAndTime";
import { ExternalApiHelper } from "src/utils/external-api-helper";
import * as fs from 'fs';
import env from 'src/config/env';
import { LoggerService } from "src/utils/logger-service";
import { Between, Equal, FindManyOptions, In, LessThanOrEqual, Like, Repository } from "typeorm";
import { SkuUpdateRecord } from "src/database/entities/product/sku-update-record.entity";
import { FilterProductsDto } from "./dto/filter-product.dto";
import { ProductAttributesOptions } from "src/database/entities/product/attribute-options.entity";

@Injectable()
export class ProductHelperService {
    constructor(
        @InjectRepository(UrlRewrites)
        private readonly urlRewritesRepository: Repository<UrlRewrites>,

        @InjectRepository(CatalogProduct)
        private readonly catalogProductRepository: Repository<CatalogProduct>,

        @InjectRepository(ProductStockAlert)
        private readonly productStockAlertRepository: Repository<ProductStockAlert>,
        @InjectRepository(SkuUpdateRecord)
        private readonly skuUpdateRecordRepository: Repository<SkuUpdateRecord>,
        @InjectRepository(ProductAttributesOptions)
        private readonly productAttributeOptionsRepository: Repository<ProductAttributesOptions>,

        private readonly logger: LoggerService,
        private readonly externalApiHelper: ExternalApiHelper,
    ) {}

    async getUrlRewriteByIds(urlRewriteIds: number[]) {
        const urlRewrites = await this.urlRewritesRepository.find({
          where: { id: In(urlRewriteIds) },
        });
        return urlRewrites;
    }

    async syncMagentoDataWithCatalog(body: any) {
        try {
          const mappedData = await this.mapMagentoDataForCatalog(body);
    
          let existingProduct = await this.catalogProductRepository.findOne({
            where: { id: mappedData.id },
          });
    
          if (!existingProduct) {
            // await this.createCatalogProduct(mappedData)
          } else {
            // await this.updateCatalogProduct(mappedData)
          }
        } catch (err) {
          console.log(err);
          this.logger.error('Failed to notify sync magento data in catalog', err);
          if (err.status === 400) {
            throw err;
          } else {
            throw new InternalServerErrorException(
              'Failed to sync magento data in catalog',
            );
          }
        }
      }

    async mapMagentoDataForCatalog(body: any) {
        try {
          return {
            id: body.id,
            sku: body.sku,
            status: body.status === 1 ? true : false,
            type_id: body.type_id,
            attributes_list: [
              ...body.custom_attributes,
              {
                attribute_code: 'name',
                value: body.name,
              },
              {
                attribute_code: 'visibility',
                value: Number(body.visibility),
              },
              {
                attribute_code: 'price',
                value: Number(body.price),
              },
            ],
            product_links: await this.transformProductLinks(
              body.product.product_links,
            ),
            inventory_details: {
              qty: body.extension_attributes.stock_item.qty,
              is_in_stock: body.extension_attributes.stock_item.is_in_stock,
              min_sale_qty: body.extension_attributes.stock_item.min_sale_qty,
              max_sale_qty: body.extension_attributes.stock_item.is_in_stock,
              backorders:
                body.extension_attributes.stock_item.is_in_stock === 1
                  ? true
                  : false,
            },
            tier_prices: await body.tier_prices.map((e) => ({
              qty: e.qty,
              value: e.value,
            })),
          };
        } catch (err) {
          console.log(err);
          this.logger.error('Failed to map magento data for catalog save', err);
          if (err.status === 400) {
            throw err;
          } else {
            throw new InternalServerErrorException(
              'Failed to map magento data for catalog save',
            );
          }
        }
      }

      async transformProductLinks(productLinks: any[]): Promise<any> {
        // Extract unique SKUs
        const skus = [
          ...new Set(productLinks.map((link) => link.linked_product_sku)),
        ];
    
        // Fetch product IDs for all SKUs
        const skuToProductIdMap = await this.fetchProductIdsBySkus(skus);
    
        // Transform product links
        const transformed: {
          [key: string]: { product_id: number; position: number }[];
        } = {};
    
        for (const link of productLinks) {
          const { link_type, linked_product_sku, position } = link;
          const product_id = skuToProductIdMap[linked_product_sku];
    
          if (product_id !== undefined) {
            // Initialize the array for the link_type if it doesn't exist
            if (!transformed[link_type]) {
              transformed[link_type] = [];
            }
    
            // Add the product link to the appropriate link_type array
            transformed[link_type].push({
              product_id,
              position,
            });
          }
        }
    
        // Remove link_type arrays with no items
        for (const key in transformed) {
          if (transformed[key].length === 0) {
            delete transformed[key];
          }
        }
    
        return transformed;
      }

      async fetchProductIdsBySkus(
        skus: string[],
      ): Promise<{ [sku: string]: number }> {
        const skuToIdMap = await this.catalogProductRepository.find({
          where: { sku: In(skus) },
        });
    
        // Filter out SKUs that are not in the mock map
        const result: { [sku: string]: number } = {};
        for (const sku of skus) {
          if (skuToIdMap[sku] !== undefined) {
            result[sku] = skuToIdMap[sku];
          }
        }
        return result;
      }

      async createProductStockAlert(productId, headers) {
        try {
          const existingProduct = await this.catalogProductRepository.findOne({
            relations: [
              // 'catalogProductFlatRelations',
              'inventoryAttributesRelations',
            ],
            where: { id: productId },
            select: {
              id: true,
              sku: true,
              type_id: true,
              created_at: true,
              updated_at: true,
              inventoryAttributesRelations: {
                id: true,
                is_in_stock: true,
              },
            },
          });
    
          if (!existingProduct) {
            throw new BadRequestException('Invalid Product or product not found');
          }
    
          if (existingProduct?.inventoryAttributesRelations?.is_in_stock === true) {
            throw new BadRequestException(
              'Requested product is already available in stock',
            );
          }
    
          const authToken = headers.authorization;
          const customerInfo =
            await this.externalApiHelper.fetchCustomerInfoInfoHelper(authToken);
    
          if (!customerInfo) {
            throw new BadRequestException(
              'Invalid customer token or customer not found',
            );
          }
    
          const existingAlert = await this.productStockAlertRepository.findOne({
            where: {
              product: productId,
              customer_email: customerInfo.email,
              customer_name: customerInfo.firstname,
              notification_status: false,
            },
          });
    
          if (existingAlert) {
            throw new BadRequestException(
              'An existing alert is already there for this customer for the requested product',
            );
          } else {
            delete existingProduct.inventoryAttributesRelations;
            // delete existingProduct.catalogProductFlatRelations;
    
            const newAlert = this.productStockAlertRepository.create({
              product: existingProduct,
              customer_email: customerInfo.email,
              customer_name: customerInfo.firstname,
              notification_status: false,
            });
    
            return await this.productStockAlertRepository.save(newAlert);
          }
        } catch (err) {
          console.log(err);
          this.logger.error(
            'Failed to create stock alert notification for the requested product',
            err,
          );
          if (err.status === 400) {
            throw err;
          } else {
            throw new InternalServerErrorException(
              'Failed to create stock alert notification for the requested product',
            );
          }
        }
      }

      async downloadStockAlertListCsv(data: any) {
        try {
          // let { old_sku, new_sku, filters } = data;
    
          const directoryPath = './reports';
          const filename = `stock_alert_data_${Date.now()}.csv`;
    
          const uploadedFilename = `stock_alert_data_${formatDateString(
            new Date(),
          )}.csv`;
    
          if (!fs.existsSync(directoryPath)) {
            fs.mkdirSync(directoryPath, { recursive: true });
          }
    
          let currentPage = 1;
          const pageSize = 200;
    
          let pagination = {
            page: currentPage,
            size: pageSize,
          };
    
          let updatedData = {
            ...data,
            pagination,
          };
    
          const filepath = `${directoryPath}/${filename}`;
    
          let { items, pages_count } = await this.listStockAlertData(updatedData);
    
          if (items.length == 0) {
            return 'No stock alert data present';
          }
    
          const writeStream = fs.createWriteStream(filepath);
    
          await this.writeStockAlertCSVHeaders(writeStream);
    
          let columns_list = [
            'sku',
            'product_name',
            'customer_name',
            'customer_email',
            'notification_status',
            'created_at',
          ];
    
          while (currentPage <= pages_count) {
            for (let item of items) {
              await this.writeStockAlertCSVRow(
                writeStream,
                {
                  sku: item.sku,
                  product_name: item?.name ?? '',
                  customer_name: item?.customer_name,
                  customer_email: item?.customer_email,
                  notification_status: item?.notification_status,
                  created_at: item.created_at
                    ? dateInBasicFormat(item.created_at)
                    : '',
                  // updated_at: item.updated_at
                  //   ? dateInBasicFormat(item.updated_at)
                  //   : '',
                },
                columns_list,
              );
            }
    
            currentPage++;
            ({ items } = await this.listStockAlertData({ ...data, pagination }));
          }
    
          writeStream.end();
          writeStream.on('finish', () => {
            console.log('CSV file generated successfully.');
          });
    
          return filename; // Return the filename for downloading
        } catch (err) {
          console.log(err);
          this.logger.error('Failed to generate csv sheet for updated skus', err);
          if (err.status === 400) {
            throw err;
          } else {
            throw new InternalServerErrorException(
              'Failed to generate csv sheet for updated skus',
            );
          }
        }
      }

      async listStockAlertData(data: any) {
        try {
          let {
            sku,
            product_name,
            customer_name,
            customer_email,
            created_at,
            pagination,
          } = data;
    
          let queryConditions: any = {
            relations: [
              'product',
              'product.catalogProductFlatRelations',
              // 'product.inventoryAttributesRelations',
            ],
          };
    
          if (sku) {
            queryConditions.where = {
              ...queryConditions.where,
              product: { sku: sku },
            };
          }
    
          if (product_name) {
            queryConditions.where = {
              ...queryConditions.where,
              product: {
                catalogProductFlatRelations: { name: Like(`%${product_name}%`) },
              },
            };
          }
    
          if (customer_name) {
            queryConditions.where = {
              ...queryConditions.where,
              customer_name,
            };
          }
    
          if (customer_email) {
            queryConditions.where = {
              ...queryConditions.where,
              customer_email,
            };
          }
    
          if (created_at) {
            if (created_at?.from !== undefined && created_at?.to !== undefined) {
              queryConditions.where = {
                ...queryConditions.where,
                created_at: Between(created_at.from, created_at.to),
              };
            } else if (created_at?.to !== undefined) {
              queryConditions.where = {
                ...queryConditions.where,
                created_at: LessThanOrEqual(created_at.to),
              };
            } else if (created_at?.from !== undefined) {
              const maxCreatedAt = await this.productStockAlertRepository
                .createQueryBuilder()
                .select('MAX(created_at)', 'maxCreatedAt')
                .getRawOne();
    
              const maxCreatedAtValue = maxCreatedAt
                ? maxCreatedAt.maxCreatedAt
                : null;
    
              queryConditions.where = {
                ...queryConditions.where,
                created_at: Between(created_at.from, maxCreatedAtValue),
              };
            }
          }
    
          // if (filters?.status !== undefined) {
          //   queryConditions.where = {
          //     ...queryConditions.where,
          //     status: filters.status,
          //   };
          // }
    
          queryConditions['skip'] =
            ((pagination?.page || 1) - 1) * env.sqlQueryResultsize;
          queryConditions['take'] = pagination?.size
            ? pagination?.size
            : env.sqlQueryResultsize;
    
          const productStockAlertDataValue =
            await this.productStockAlertRepository.findAndCount({
              ...queryConditions,
              order: { id: 'DESC' },
              select: {
                id: true,
                customer_name: true,
                customer_email: true,
                notification_status: true,
                created_at: true,
                updated_at: true,
                product: {
                  id: true,
                  sku: true,
                  catalogProductFlatRelations: {
                    id: true,
                    name: true,
                  },
                },
              },
            });
    
          let productStockAlertDataArray = productStockAlertDataValue[0];
          let productStockAlertDataCount = productStockAlertDataValue[1];
    
          let modifiedData = productStockAlertDataArray.map((item) => {
            let data = {
              id: item.id,
              customer_name: item.customer_name,
              customer_email: item.customer_email,
              created_at: item.created_at,
              updated_at: item.updated_at,
              notification_status: item.notification_status,
              sku: item.product.sku,
              name: item.product.catalogProductFlatRelations.name,
            };
            return data;
          });
    
          let countPerPage = pagination?.page
            ? pagination.size
            : env.sqlQueryResultsize;
    
          return {
            item_count: productStockAlertDataCount,
            pages_count: Math.ceil(productStockAlertDataCount / countPerPage),
            page_no: pagination?.page,
            page_size: productStockAlertDataArray.length,
            items: modifiedData,
          };
        } catch (err) {
          console.log(err);
          this.logger.error('Failed to get products stock alert list', err);
          if (err.status === 400) {
            throw err;
          } else {
            throw new InternalServerErrorException(
              'Failed to get products stock alert list',
            );
          }
        }
      }
    

      private async writeStockAlertCSVRow(
        writeStream: fs.WriteStream,
        columns_list: any,
        required_columns,
      ) {
        const valuesArray = [
          columns_list.sku,
          columns_list.product_name,
          columns_list.customer_name,
          columns_list.customer_email,
          columns_list.notification_status,
          columns_list.created_at,
          // columns_list.updated_at,
        ];
    
        writeStream.write(`${valuesArray.join(',')}\n`);
      }
    
      private async writeStockAlertCSVHeaders(writeStream: fs.WriteStream) {
        let column_headers = [
          'sku',
          'product_name',
          'customer_name',
          'customer_email',
          'notification_status',
          'created_at',
          // 'updated_at',
        ];
    
        writeStream.write(`${column_headers.join(',')}\n`);
      }

      async downloadskuUpdateListCsv(data: any) {
        try {
          // let { old_sku, new_sku, filters } = data;
    
          const directoryPath = './reports';
          const filename = `sku_update_data_${Date.now()}.csv`;
    
          const uploadedFilename = `sku_update_data_${formatDateString(
            new Date(),
          )}.csv`;
    
          if (!fs.existsSync(directoryPath)) {
            fs.mkdirSync(directoryPath, { recursive: true });
          }
    
          let currentPage = 1;
          const pageSize = 200;
    
          let pagination = {
            page: currentPage,
            size: pageSize,
          };
    
          let updatedData = {
            ...data,
            pagination,
          };
    
          const filepath = `${directoryPath}/${filename}`;
    
          let { items, pages_count } = await this.listUpdatedSkus(updatedData);
    
          const writeStream = fs.createWriteStream(filepath);
    
          await this.writeSkuUpdateCSVHeaders(writeStream);
    
          let columns_list = [
            'product_id',
            'old_sku',
            'new_sku',
            'status',
            'created_at',
            'updated_at',
          ];
    
          while (currentPage <= pages_count) {
            for (let item of items) {
              await this.writeSkuUpdateCSVRow(
                writeStream,
                {
                  product_id: item.id,
                  old_sku: item.old_sku,
                  new_sku: item?.new_sku ?? '',
                  status: item?.status,
                  created_at: item.created_at
                    ? dateInBasicFormat(item.created_at)
                    : '',
                  updated_at: item.updated_at
                    ? dateInBasicFormat(item.updated_at)
                    : '',
                },
                columns_list,
              );
            }
    
            currentPage++;
            ({ items } = await this.listUpdatedSkus({ ...data, pagination }));
          }
    
          writeStream.end();
          writeStream.on('finish', () => {
            console.log('CSV file generated successfully.');
          });
    
          return filename; // Return the filename for downloading
        } catch (err) {
          console.log(err);
          this.logger.error('Failed to generate csv sheet for updated skus', err);
          if (err.status === 400) {
            throw err;
          } else {
            throw new InternalServerErrorException(
              'Failed to generate csv sheet for updated skus',
            );
          }
        }
      }

      private async writeSkuUpdateCSVRow(
        writeStream: fs.WriteStream,
        columns_list: any,
        required_columns,
      ) {
        const row = {
          product_id: columns_list.product_id,
          old_sku: columns_list.old_sku,
          new_sku: columns_list.new_sku,
          status: columns_list.status,
        };
    
        const valuesArray = [
          columns_list.product_id,
          columns_list.old_sku,
          columns_list.new_sku,
          columns_list.status,
          columns_list.created_at,
          columns_list.updated_at,
        ];
    
        writeStream.write(`${valuesArray.join(',')}\n`);
      }
    
    
      private async writeSkuUpdateCSVHeaders(writeStream: fs.WriteStream) {
        let column_headers = [
          'product_id',
          'old_sku',
          'new_sku',
          'status',
          // 'error',
          'created_at',
          'updated_at',
        ];
    
        writeStream.write(`${column_headers.join(',')}\n`);
      }
    
      async listUpdatedSkus(data: any) {
        try {
          let { old_sku, new_sku, filters, pagination, sort_by } = data;
    
          // Validate that only one of old_sku or new_sku is present
          if (old_sku && new_sku) {
            throw new BadRequestException(
              'Please provide only one SKU: either old_sku or new_sku.',
            );
          }
    
          let queryConditions: any = {};
    
          if (old_sku) {
            queryConditions.where = {
              ...queryConditions.where,
              old_sku: old_sku,
            };
          }
          if (new_sku) {
            queryConditions.where = {
              ...queryConditions.where,
              new_sku: new_sku,
            };
          }
    
          if (old_sku || new_sku) {
            const skuUpdateRecords = await this.skuUpdateRecordRepository.findOne({
              ...queryConditions,
            });
    
            if (!skuUpdateRecords) {
              throw new BadRequestException('Searched SKU not found');
            } else {
              // return skuUpdateRecords;
              return {
                item_count: 1,
                pages_count: 1,
                page_no: 1,
                page_size: 1,
                items: [skuUpdateRecords],
              };
            }
          }
    
          if (filters?.created_at) {
            if (
              filters.created_at?.from !== undefined &&
              filters.created_at?.to !== undefined
            ) {
              queryConditions.where = {
                ...queryConditions.where,
                created_at: Between(filters.created_at.from, filters.created_at.to),
              };
            } else if (filters.created_at?.to !== undefined) {
              queryConditions.where = {
                ...queryConditions.where,
                created_at: LessThanOrEqual(filters.created_at.to),
              };
            } else if (filters.created_at?.from !== undefined) {
              const maxCreatedAt = await this.skuUpdateRecordRepository
                .createQueryBuilder()
                .select('MAX(created_at)', 'maxCreatedAt')
                .getRawOne();
    
              const maxCreatedAtValue = maxCreatedAt
                ? maxCreatedAt.maxCreatedAt
                : null;
    
              queryConditions.where = {
                ...queryConditions.where,
                created_at: Between(filters.created_at.from, maxCreatedAtValue),
              };
            }
          }
    
          if (filters?.updated_at) {
            if (
              filters.updated_at?.from !== undefined &&
              filters.updated_at?.to !== undefined
            ) {
              queryConditions.where = {
                ...queryConditions.where,
                updated_at: Between(filters.updated_at.from, filters.updated_at.to),
              };
            } else if (filters.updated_at?.to !== undefined) {
              queryConditions.where = {
                ...queryConditions.where,
                updated_at: LessThanOrEqual(filters.updated_at.to),
              };
            } else if (filters.updated_at?.from !== undefined) {
              const maxUpdatedAt = await this.skuUpdateRecordRepository
                .createQueryBuilder()
                .select('MAX(updated_at)', 'maxUpdatedAt')
                .getRawOne();
    
              const maxUpdatedAtValue = maxUpdatedAt
                ? maxUpdatedAt.maxUpdatedAt
                : null;
    
              queryConditions.where = {
                ...queryConditions.where,
                updated_at: Between(filters.updated_at.from, maxUpdatedAtValue),
              };
            }
          }
    
          if (filters?.status !== undefined) {
            queryConditions.where = {
              ...queryConditions.where,
              status: filters.status,
            };
          }
    
          queryConditions['skip'] =
            ((pagination?.page || 1) - 1) * env.sqlQueryResultsize;
          queryConditions['take'] = pagination?.size
            ? pagination?.size
            : env.sqlQueryResultsize;
    
          const skuUpdatesValue = await this.skuUpdateRecordRepository.findAndCount(
            {
              ...queryConditions,
              order: { id: 'DESC' },
            },
          );
    
          let skuUpdatesDataArray = skuUpdatesValue[0];
          let skuUpdatesCount = skuUpdatesValue[1];
    
          let countPerPage = pagination?.page
            ? pagination?.size
            : env.sqlQueryResultsize;
    
          return {
            item_count: skuUpdatesCount,
            pages_count: Math.ceil(skuUpdatesCount / countPerPage),
            page_no: pagination?.page,
            page_size: skuUpdatesDataArray.length,
            items: skuUpdatesDataArray,
          };
        } catch (err) {
          console.log(err);
          this.logger.error('Failed to get sku update list', err);
          if (err.status === 400) {
            throw err;
          } else {
            throw new InternalServerErrorException('Failed to get sku update list');
          }
        }
      }

      async getPaginatedProducts(
        paginationDto: FilterProductsDto,
        productIds?: Number[],
      ) {
        try {
          const {
            page = 1,
            limit = 10,
            sku,
            name,
            id,
            id_from,
            id_to,
            status,
            price_from,
            price_to,
            visibility,
            type_id,
            sort_by = 'id',
            sort_order = 'DESC',
          } = paginationDto;
    
          const options: FindManyOptions<CatalogProduct> = {
            relations: ['catalogProductFlatRelations'],
            where: {},
            select: {
              id: true,
              sku: true,
              type_id: true,
              status: true,
              catalogProductFlatRelations: {
                name: true,
                price: true,
                visibility: true,
                thumbnail: true,
              },
            },
            skip: (page - 1) * limit,
            take: limit,
          };
    
          if (sort_by === 'id') {
            options.order = { id: sort_order };
          } else if (sort_by === 'price') {
            options.order = {
              catalogProductFlatRelations: { price: sort_order },
            };
          } else {
            throw new BadRequestException('Invalid sort_by value provided');
          }
    
          const fetchOptions = await this.productAttributeOptionsRepository.find({
            where: { attribute: { code: 'visibility' } },
            relations: ['attribute'],
          });
    
          var OptionsIdValueMap = new Map();
          fetchOptions.forEach((e) => {
            OptionsIdValueMap.set(e.id, e.value);
          });
    
          const where: any = {};
    
          if (productIds?.length > 0) where.id = In(productIds);
          if (status !== undefined) where.status = Equal(status);
          if (type_id !== undefined) where.type_id = type_id;
    
          let filteredArray;
    
          if (sku) {
            where.sku = sku;
          }
    
          if (id) {
            where.id = id;
          }
    
          if (id_from !== undefined) {
            filteredArray = productIds.filter((item) => Number(item) >= id_from);
            where.id = In(filteredArray);
          }
    
          if (id_to !== undefined) {
            filteredArray = productIds.filter((item) => Number(item) <= id_to);
            where.id = In(filteredArray);
          }
    
          if (id_from && id_to) {
            filteredArray = productIds.filter(
              (item) => Number(item) >= id_from && Number(item) <= id_to,
            );
            where.id = In(filteredArray);
          }
    
          if (name) {
            where.catalogProductFlatRelations = {
              name: Like(`%${name}%`),
            };
          }
          if (price_from !== undefined || price_to !== undefined) {
            where.catalogProductFlatRelations = {
              ...where.catalogProductFlatRelations,
              price: Between(price_from || 0, price_to || Number.MAX_VALUE),
            };
          }
    
          if (visibility !== undefined) {
            where.catalogProductFlatRelations = {
              ...where.catalogProductFlatRelations,
              visibility: visibility,
            };
          }
          options.where = where;
          const [products, totalCount] =
            await this.catalogProductRepository.findAndCount(options);
    
          let mappedData = await Promise.all(
            products.map(async (e) => {
              if (e.catalogProductFlatRelations?.visibility) {
                let optionsKey = e.catalogProductFlatRelations.visibility;
    
                e.catalogProductFlatRelations.visibility =
                  OptionsIdValueMap.get(optionsKey) || null;
              }
    
              let data = {
                id: e.id,
                sku: e.sku,
                status: e.status,
                type_id: e.type_id,
                catalogProductFlatRelations: { ...e.catalogProductFlatRelations },
              };
              return data;
            }),
          );
    
          return {
            products: mappedData,
            count: totalCount,
            page,
            limit,
          };
        } catch (e) {
          throw new InternalServerErrorException(e?.message || e);
        }
      }

      async getFilteredProducts(filterDto: FilterProductsDto) {
        return this.getPaginatedProducts(filterDto);
      }
}
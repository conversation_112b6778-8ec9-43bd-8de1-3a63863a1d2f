import {
  IsO<PERSON>al,
  IsString,
  IsNumber,
  IsInt,
  Min,
  <PERSON>,
  IsBoolean,
  IsEnum,
  IsIn,
  IsDateString,
  ValidateNested,
  IsObject,
} from 'class-validator';
import { Type, Transform } from 'class-transformer';

/**
 * DTO for search_by_keyword parameter
 */
export class  SearchByKeywordDto {
  @IsString()
  @IsIn(['name', 'sku', 'url_key'], {
    message: 'search_by_keyword.field must be either "name" or "sku" or "url_key"'
  })
  field: 'name' | 'sku' | 'url_key';

  @IsString()
  value: string;
}

/**
 * DTO for sort_by parameter
 */
export class SortByDto {
  @IsString()
  @IsIn(['ASC', 'DESC', 'asc', 'desc'], {
    message: 'sort_by.order must be "ASC" or "DESC"'
  })
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      return value.toUpperCase();
    }
    return value;
  })
  order: 'ASC' | 'DESC';

  @IsString()
  field: string;
}

/**
 * DTO for pagination parameter
 */
export class PaginationDto {
  @IsOptional()
  @IsInt()
  @Min(1)
  @Type(() => Number)
  page?: number = 1;

  @IsOptional()
  @IsInt()
  @Min(1)
  @Max(100)
  @Type(() => Number)
  size?: number = 10;
}

/**
 * DTO for filters parameter
 */
export class FiltersDto {
  // String filters
  @IsOptional()
  @IsString()
  name?: string;

  @IsOptional()
  @IsString()
  sku?: string;

  @IsOptional()
  @IsString()
  url_key?: string;

  @IsOptional()
  @IsString()
  gstin?: string;

  // Boolean filters
  // @IsOptional()
  // @IsBoolean()
  // @Transform(({ value }) => {
  //   if (typeof value === 'string') {
  //     return value.toLowerCase() === 'true';
  //   }
  //   return value;
  // })
  // status?: boolean;

  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  status?: number;

  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      return value.toLowerCase() === 'true';
    }
    return value;
  })
  backorders?: boolean;

  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      return value.toLowerCase() === 'true';
    }
    return value;
  })
  demo_available?: boolean;

  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      return value.toLowerCase() === 'true';
    }
    return value;
  })
  is_in_stock?: boolean;

  // String type filter
  @IsOptional()
  @IsString()
  type_id?: string;

  // Number filters
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  visibility?: number;

  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  manufacturer?: number;

  // Range filters - ID
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  id_from?: number;

  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  id_to?: number;

  // Range filters - Price (can be string or number based on current implementation)
  @IsOptional()
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      const parsed = parseFloat(value);
      return isNaN(parsed) ? undefined : parsed;
    }
    return value;
  })
  @IsNumber()
  price_from?: number;

  @IsOptional()
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      const parsed = parseFloat(value);
      return isNaN(parsed) ? undefined : parsed;
    }
    return value;
  })
  @IsNumber()
  price_to?: number;

  // Range filters - Product Expiry (string dates)
  @IsOptional()
  @IsString()
  product_expiry_from?: string;

  @IsOptional()
  @IsString()
  product_expiry_to?: string;

  // Range filters - Quantity
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  min_qty?: number;

  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  max_qty?: number;

  // Range filters - MSRP
  @IsOptional()
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      const parsed = parseFloat(value);
      return isNaN(parsed) ? undefined : parsed;
    }
    return value;
  })
  @IsNumber()
  msrp_from?: number;

  @IsOptional()
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      const parsed = parseFloat(value);
      return isNaN(parsed) ? undefined : parsed;
    }
    return value;
  })
  @IsNumber()
  msrp_to?: number;

  // Legacy filters (commented in controller but might be used)
  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      return value.toLowerCase() === 'true';
    }
    return value;
  })
  showCategoryNames?: boolean;

  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      return value.toLowerCase() === 'true';
    }
    return value;
  })
  showOptionsValues?: boolean;

  // Quantity filter (legacy - might be used in service)
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  quantity?: number;
}

/**
 * Main DTO for listProducts request body
 * This matches the exact structure used in the current implementation
 */
export class ListProductsDto {
  @IsOptional()
  @ValidateNested()
  @Type(() => FiltersDto)
  filters?: FiltersDto = {};

  @IsOptional()
  @ValidateNested()
  @Type(() => PaginationDto)
  pagination?: PaginationDto = { page: 1, size: 10 };

  @IsOptional()
  @ValidateNested()
  @Type(() => SearchByKeywordDto)
  search_by_keyword?: SearchByKeywordDto;

  @IsOptional()
  @ValidateNested()
  @Type(() => SortByDto)
  sort_by?: SortByDto;

  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  product_id?: number;
}

/**
 * DTO for query parameters that get transformed into ListProductsDto
 * This handles all the individual query parameters from the controller
 */
export class ListProductsQueryDto {
  // Search parameters
  @IsOptional()
  @IsString()
  search_by_keyword_field?: string;

  @IsOptional()
  @IsString()
  search_by_keyword_value?: string;

  // Sort parameters
  @IsOptional()
  @IsString()
  sort_by_order?: string;

  @IsOptional()
  @IsString()
  sort_by_field?: string;

  // Product ID
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  product_id?: number;

  // Pagination
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  page?: number;

  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  size?: number;

  // Range filters - ID
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  id_from?: number;

  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  id_to?: number;

  // String filters
  @IsOptional()
  @IsString()
  name?: string;

  @IsOptional()
  @IsString()
  sku?: string;

  @IsOptional()
  @IsString()
  url_key?: string;

  @IsOptional()
  @IsString()
  gstin?: string;

  // Boolean filters
  // @IsOptional()
  // @IsBoolean()
  // @Transform(({ value }) => {
  //   if (typeof value === 'string') {
  //     return value.toLowerCase() === 'true';
  //   }
  //   return value;
  // })
  // status?: boolean;

  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  status?: number;

  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      return value.toLowerCase() === 'true';
    }
    return value;
  })
  backorders?: boolean;

  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      return value.toLowerCase() === 'true';
    }
    return value;
  })
  demo_available?: boolean;

  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      return value.toLowerCase() === 'true';
    }
    return value;
  })
  is_in_stock?: boolean;

  // String type filter
  @IsOptional()
  @IsString()
  type_id?: string;

  // Number filters
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  visibility?: number;

  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  manufacturer?: number;

  // Range filters - Price
  @IsOptional()
  @IsString()
  price_from?: string;

  @IsOptional()
  @IsString()
  price_to?: string;

  // Range filters - Product Expiry
  @IsOptional()
  @IsString()
  product_expiry_from?: string;

  @IsOptional()
  @IsString()
  product_expiry_to?: string;

  // Range filters - Quantity
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  min_qty?: number;

  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  max_qty?: number;

  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  quantity?: number;
  // Range filters - MSRP
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  msrp_from?: number;

  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  msrp_to?: number;
}

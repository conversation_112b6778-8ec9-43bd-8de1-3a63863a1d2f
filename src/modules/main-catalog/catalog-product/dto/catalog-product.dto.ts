import { Type } from 'class-transformer';
import { PartialType } from '@nestjs/mapped-types';
import {
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsPositive,
  IsString,
  IsArray,
  IsIn,
  IsInt,
  IsBoolean,
  IsObject,
  IsEnum,
  ValidateNested,
  isInt,
  ArrayNotEmpty,
  Matches,
  Min,
} from 'class-validator';

export enum TypeIdValues {
  Simple = 'simple',
  Grouped = 'grouped',
  Virtual = 'virtual',
}

export enum BackendInputs_Enum {
  VARCHAR = 'varchar',
  TEXT = 'text',
  STATIC = 'static',
  DECIMAL = 'decimal',
  DATETIME = 'datetime',
  INT = 'int',
  BOOLEAN = 'boolean',
}

export enum FrontendInputs_Enum {
  TEXT = 'text',
  TEXTAREA = 'textarea',
  PRICE = 'price',
  DATE = 'date',
  WEIGHT = 'weight',
  MEDIA_IMAGE = 'media_image',
  GALLERY = 'gallery',
  SELECT = 'select',
  BOOLEAN = 'boolean',
  HIDDEN = 'hidden',
}

export enum TierPriceCustomerGroupEnum {
  ALL_GROUPS = 'ALL GROUPS',
  NOT_LOGGED_IN = 'NOT LOGGED IN',
  GENERAL = 'General',
  DENTALKART_VIP = 'Dentalkart VIP',
}

export enum TierPriceCustomerGroupPricesEnum {
  FIXED = 'Fixed',
  DISCOUNT = 'Discount',
}

export class CreateCatalogProductAttributesDto {
  @IsOptional()
  @IsNumber()
  id: number;

  @IsNotEmpty()
  @IsString()
  code: string;

  @IsNotEmpty()
  @IsString()
  label: string;

  @IsOptional()
  @IsBoolean()
  is_unique: boolean;

  @IsOptional()
  @IsBoolean()
  is_required: boolean;

  @IsOptional()
  @IsBoolean()
  is_sortable: boolean;

  @IsOptional()
  @IsBoolean()
  is_filterable: boolean;

  @IsOptional()
  @IsBoolean()
  is_editable: boolean;

  @IsOptional()
  @IsBoolean()
  is_deletable: boolean;

  @IsOptional()
  @IsString()
  default_value: string;

  @IsNotEmpty()
  @IsEnum(BackendInputs_Enum)
  backend_type: BackendInputs_Enum;

  @IsNotEmpty()
  @IsEnum(FrontendInputs_Enum)
  frontend_input: FrontendInputs_Enum;

  @IsOptional()
  @IsString()
  @Matches(/^(simple|grouped|virtual)(,(simple|grouped|virtual))*$/, {
    message: 'Invalid value(s) provided for apply_to',
  })
  apply_to: string;
}

export class UpdateCatalogProductAttributesDto {
  @IsNotEmpty()
  @IsNumber()
  id: number;

  @IsOptional()
  @IsString()
  code: string;

  @IsOptional()
  @IsString()
  label: string;

  @IsOptional()
  @IsBoolean()
  is_unique: boolean;

  @IsOptional()
  @IsBoolean()
  is_required: boolean;

  @IsOptional()
  @IsBoolean()
  is_sortable: boolean;

  @IsOptional()
  @IsBoolean()
  is_filterable: boolean;

  @IsOptional()
  @IsBoolean()
  is_editable: boolean;

  @IsOptional()
  @IsBoolean()
  is_deletable: boolean;

  @IsOptional()
  @IsString()
  default_value: string;

  @IsOptional()
  @IsString()
  backend_type: string;

  @IsOptional()
  @IsString()
  frontend_input: string;

  @IsOptional()
  @IsString()
  @Matches(/^(simple|grouped|virtual)(,(simple|grouped|virtual))*$/, {
    message: 'Invalid value(s) provided for apply_to',
  })
  apply_to: string;
}

export class CreateAttributesGroupDto {
  @IsOptional()
  @IsNumber()
  id: number;

  @IsNotEmpty()
  @IsString()
  group_name: string;

  @IsOptional()
  @IsString()
  group_code: string;

  @IsNotEmpty()
  @IsNumber()
  sort_order: number;
}

export class UpdateAttributesGroupDto {
  @IsNotEmpty()
  @IsNumber()
  id: number;

  @IsOptional()
  @IsString()
  group_name: string;

  @IsOptional()
  @IsString()
  group_code: string;

  @IsOptional()
  @IsNumber()
  sort_order: number;
}

export class CreateAttributesGroupRelationDto {
  @IsOptional()
  @IsNumber()
  id: number;

  @IsNotEmpty()
  @IsNumber()
  group_id: number;

  @IsNotEmpty()
  @IsNumber()
  attribute_id: number;

  @IsNotEmpty()
  @IsNumber()
  sort_order: number;
}

class GroupAttributeItem {
  @IsInt()
  group_id: number;

  @IsArray()
  @ArrayNotEmpty()
  @Type(() => Number)
  attribute_id: number[];
}

class InventoryDetailsDto {
  @IsInt()
  @IsOptional()
  @Min(0, { message: 'Qty must be a non-negative integer' })
  qty: number;

  @IsBoolean()
  @IsOptional()
  is_in_stock: boolean;

  @IsInt()
  @IsOptional()
  @Min(1, { message: 'Min sale qty must be a positive integer' })
  min_sale_qty: number;

  @IsInt()
  @IsOptional()
  @Min(1, { message: 'Max sale qty must be a positive integer' })
  max_sale_qty: number;

  @IsBoolean()
  @IsOptional()
  backorders: boolean;
}

class MediaGalleryDto {
  @IsString()
  @IsNotEmpty()
  value: string;

  @IsInt()
  @IsNotEmpty()
  position: number;

  @IsBoolean()
  @IsNotEmpty()
  is_disabled: boolean;
}

class TierPriceDto {
  @IsInt()
  @IsNotEmpty()
  qty: number;

  @IsNumber()
  @IsNotEmpty()
  value: number;

  @IsOptional()
  @IsEnum(TierPriceCustomerGroupEnum)
  customer_group?: TierPriceCustomerGroupEnum;

  @IsOptional()
  @IsEnum(TierPriceCustomerGroupPricesEnum)
  price_type?: TierPriceCustomerGroupPricesEnum;
}

export class GroupTierPriceItemDto {
  // @IsNumber()
  // price_group: number;

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => TierPriceDto)
  tier_prices: TierPriceDto[];

  // @IsArray()
  // @IsNumber({}, { each: true })
  // child_ids: number[];
}

export class GroupTierPriceDto {
  @ValidateNested({ each: true })
  @Type(() => GroupTierPriceItemDto)
  group_tier_price: Record<string, GroupTierPriceItemDto>;
}

export class AttributeDto {
  @IsString()
  @IsNotEmpty()
  attribute_code: string;

  @IsOptional()
  value: any;

  @IsOptional()
  @IsBoolean()
  enableRedirect: boolean;
}

class ProductLinksDto {
  @IsArray()
  @IsOptional()
  associated?: any[];

  @IsArray()
  @IsOptional()
  crosssell?: any[];

  @IsArray()
  @IsOptional()
  upsell?: any[];

  @IsArray()
  @IsOptional()
  related?: any[];
}

export class UserMetaInfo {
  @IsString()
  @IsOptional()
  browser_name: string;

  @IsString()
  @IsOptional()
  device: string;

  @IsString()
  @IsOptional()
  version: string;

  @IsString()
  @IsOptional()
  platform: string;
}

export class CreateCatalogProductDto {
  @IsInt()
  @IsOptional()
  id: number;

  // @IsString()
  // @IsNotEmpty()
  // sku: string;

  @IsInt()
  @IsOptional()
  attribute_set_id: number;

  @IsNotEmpty()
  status: boolean;

  @IsString()
  @IsNotEmpty()
  @IsEnum(TypeIdValues)
  type_id: TypeIdValues;

  @IsArray()
  @IsOptional()
  // @IsInt({ each: true })
  // @IsNotEmpty()
  category_associated: number[];

  @IsOptional()
  @ValidateNested()
  @Type(() => ProductLinksDto)
  product_links: ProductLinksDto;

  @ValidateNested()
  @Type(() => InventoryDetailsDto)
  inventory_details: InventoryDetailsDto;

  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => TierPriceDto)
  tier_prices: TierPriceDto[];

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => AttributeDto)
  attributes_list: AttributeDto[];

  // @IsString()
  // @IsNotEmpty()
  // admin_identifier: string;

  // @IsObject()
  // @IsOptional()
  // user_meta_info: UserMetaInfo;
}

export class UpdateCatalogProductDto {
  // @IsInt()
  // @IsOptional()
  // id: number;

  @IsOptional()
  status: boolean;

  @IsArray()
  @IsInt({ each: true })
  @IsOptional()
  category_associated: number[];

  @ValidateNested()
  @Type(() => ProductLinksDto)
  @IsOptional()
  product_links: ProductLinksDto;

  @ValidateNested()
  @Type(() => InventoryDetailsDto)
  @IsOptional()
  inventory_details: InventoryDetailsDto;

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => TierPriceDto)
  @IsOptional()
  tier_prices: TierPriceDto[];

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => AttributeDto)
  @IsOptional()
  attributes_list: AttributeDto[];

  // @IsString()
  // @IsNotEmpty()
  // admin_identifier: string;

  @IsOptional()
  updated_by_action_details: any;

  // @IsObject()
  // @IsOptional()
  // user_meta_info: UserMetaInfo;
}

export class UpdateMultipleProductsStatusDto {
  @IsArray()
  @ArrayNotEmpty()
  @IsNumber({}, { each: true })
  product_ids: number[];

  @IsBoolean()
  newStatus: boolean;
}

export class ViniculumInventoryItemDto {
  @IsNotEmpty()
  @IsNumber()
  qty: number;

  @IsNotEmpty()
  @IsBoolean()
  is_in_stock: boolean;
}

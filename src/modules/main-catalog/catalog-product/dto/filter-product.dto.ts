import {
  IsOptional,
  IsString,
  IsNumber,
  IsInt,
  Min,
  IsBoolean,
  IsEnum,
  IsIn,
} from 'class-validator';
import { Type, Transform } from 'class-transformer';

export enum ProductTypeEnum {
  SIMPLE = 'simple',
  GROUPED = 'grouped',
  BUNDLED = 'virtual',
}
export class FilterProductsDto {
  @IsOptional()
  @IsString()
  sku?: string;

  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  id?: number;

  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  id_from?: number;

  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  id_to?: number;

  @IsOptional()
  @IsString()
  name?: string;

  @IsOptional()
  @IsInt()
  @Min(1)
  @Transform(({ value }) => parseInt(value, 10))
  page: number = 1;

  @IsOptional()
  @IsInt()
  @Min(1)
  @Transform(({ value }) => parseInt(value, 10))
  limit: number = 10;

  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => value === 'true')
  status?: boolean;

  @IsOptional()
  @IsEnum(ProductTypeEnum)
  type_id?: ProductTypeEnum;

  @IsOptional()
  @IsNumber()
  @Transform(({ value }) => parseInt(value, 10))
  price_from?: number;

  @IsOptional()
  @IsNumber()
  @Transform(({ value }) => parseInt(value, 10))
  price_to?: number;

  @IsNumber()
  @IsOptional()
  @Transform(({ value }) => parseInt(value, 10))
  visibility?: number;

  @IsOptional()
  @IsString()
  @IsIn(['id', 'price'])
  sort_by?: 'id' | 'price';

  @IsOptional()
  @IsString()
  @IsIn(['ASC', 'DESC'])
  sort_order?: 'ASC' | 'DESC';
}

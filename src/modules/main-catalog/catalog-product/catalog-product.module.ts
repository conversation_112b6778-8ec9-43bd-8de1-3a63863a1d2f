import { Module } from '@nestjs/common';
import { CatalogProductService } from './catalog-product.service';
import {
  CatalogProductController,
  CatalogProductControllerForViniculum,
} from './catalog-product.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ProductAttributeName } from 'src/database/entities/product/product-attribute-name.entity';
import { CatalogProduct } from 'src/database/entities/product/main-product.entity';
import { AttributesGroup } from 'src/database/entities/product/attributes-group.entity';
import { AttributesGroupRelation } from 'src/database/entities/product/attributes-group-relation.entity';
import { ProductAttributesOptions } from 'src/database/entities/product/attribute-options.entity';
import { IntegerAttributeValues } from 'src/database/entities/product/integer-attribute-values.entity';
import { BooleanAttributeValues } from 'src/database/entities/product/boolean-attribute-values.entity';
import { DateAttributeValues } from 'src/database/entities/product/date-attribute-values.entity';
import { DecimalAttributeValues } from 'src/database/entities/product/decimal-attribute-values.entity';
import { TextAttributeValues } from 'src/database/entities/product/text-attribute-values.entity';
import { StringAttributeValues } from 'src/database/entities/product/string-attibute-values.entity';
import { ProductCategoryRelation } from 'src/database/entities/category/product-category-relation.entity';
import { TierPrices } from 'src/database/entities/product/tier-prices.entity';
import { InventoryAttributes } from 'src/database/entities/product/inventory-attributes-values.entity';
import { CatalogProductRelation } from 'src/database/entities/product/catalog-product-relation.entity';
import { CatalogCategory } from 'src/database/entities/category/main-category.entity';
import { MediaGallary } from 'src/database/entities/product/media-gallary.entity';
import { MediaGallaryVideo } from 'src/database/entities/product/media-gallary-video.entity';
import { CatalogProductSubscriber } from './product-subscriber';
import { LoggerService } from 'src/utils/logger-service';
import { S3Service } from 'src/utils/s3service';
import { UtilsModule } from 'src/utils/utils.module';
import { CatalogProductFlat } from 'src/database/entities/product/main-product-flat.entity';
import { ProductDataCsvUrls } from 'src/database/entities/product/product-data-csv-urls.entity';
import { BulkAttributesUpdateStatus } from 'src/database/entities/product/bulk-attributes-update-status.entity';
import { Activity } from 'src/database/entities/product/activity.entity';
import { ActivityLogs } from 'src/database/entities/product/activity-logs.entity';
import { EventsOutbox } from 'src/database/entities/outbox/event-outbox.entity';
import { UrlRewrites } from 'src/database/entities/product/url-rewrites.entity';
import { SkuUpdateRecord } from 'src/database/entities/product/sku-update-record.entity';
import { ProductStockAlert } from 'src/database/entities/product/product-stock-alert.entity';
import { QuestionAnswer } from 'src/database/entities/product/product-question-answer.entity';
import { ProductHelperService } from './product-helper.service';
// import { ProductResponseMapperV2Helper } from './helpers/product-response-mapper-v2.helper';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      ProductAttributeName,
      CatalogProduct,
      AttributesGroup,
      AttributesGroupRelation,
      ProductAttributesOptions,
      IntegerAttributeValues,
      BooleanAttributeValues,
      DateAttributeValues,
      DecimalAttributeValues,
      TextAttributeValues,
      StringAttributeValues,
      ProductCategoryRelation,
      TierPrices,
      InventoryAttributes,
      CatalogProductRelation,
      CatalogCategory,
      ProductCategoryRelation,
      MediaGallary,
      MediaGallaryVideo,
      CatalogProductFlat,
      ProductDataCsvUrls,
      BulkAttributesUpdateStatus,
      Activity,
      ActivityLogs,
      EventsOutbox,
      UrlRewrites,
      SkuUpdateRecord,
      ProductStockAlert,
      QuestionAnswer,
    ]),
    UtilsModule,
  ],
  providers: [CatalogProductService,ProductHelperService],
  controllers: [CatalogProductController, CatalogProductControllerForViniculum],
  exports: [CatalogProductService, ProductHelperService],
})
export class CatalogProductModule {}

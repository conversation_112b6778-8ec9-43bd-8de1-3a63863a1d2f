import { CatalogProduct } from 'src/database/entities/product/main-product.entity';
import {
  DataSource,
  EntitySubscriberInterface,
  EventSubscriber,
  InsertEvent,
  Repository,
} from 'typeorm';
import { CatalogProductService } from './catalog-product.service';
import { forwardRef, Inject, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { EventEmitter } from 'typeorm/platform/PlatformTools';

@EventSubscriber()
export class CatalogProductSubscriber
  implements EntitySubscriberInterface<CatalogProduct>
{
  //   private readonly productService: CatalogProductService;
  constructor(
    private datasource: DataSource,
    private readonly productService: CatalogProductService,
  ) {
    // datasource.subscribers.push(this);
  }

  listenTo() {
    return CatalogProduct;
  }

  async afterInsert(event: InsertEvent<CatalogProduct>) {
    const createdProduct = event.entity;
    // console.log(this, 'this');
    console.log(createdProduct);
    // await this.productService.syncProductwithElasticSearch(createdProduct);
  }
}

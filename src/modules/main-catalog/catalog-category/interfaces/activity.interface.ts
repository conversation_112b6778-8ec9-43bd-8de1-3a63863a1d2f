import { EntityType } from 'src/database/entities/outbox/event-outbox.entity';
import { EntityTypeEnum } from 'src/database/entities/product/activity.entity';
import { QueryRunner } from 'typeorm';

export type ActivityType =
  | 'create-category'
  | 'update-category'
  | 'category-products-update'
  | 'category-position-parent-change'
  | 'create-category-url-rewrite'
  | 'modify-category-url-rewrite'
  | 'create-product-url-rewrite'
  | 'modify-product-url-rewrite'
  | 'create-category-banner'
  | 'update-category-banner'
  | 'delete-category-banner';

export interface ActivityAndEvent<T> {
  activityType: ActivityType;
  updatedValue: T;
  previousValue: T | null;
  queryRunner: QueryRunner;
  headers: Record<string, string | string[]>;
  entityId: number;
  entityType: EntityType;
  activityEntityType: EntityTypeEnum;
}

import {
  ArgumentMetadata,
  BadRequestException,
  Injectable,
  PipeTransform,
} from '@nestjs/common';
import { Transform, Type } from 'class-transformer';
import {
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsPositive,
  IsString,
  IsArray,
  IsIn,
  IsInt,
  IsBoolean,
  IsObject,
  IsEnum,
  ValidateNested,
  isInt,
  Matches,
  Length,
  IsBooleanString,
} from 'class-validator';

export class CreateCatalogCategoryDto {
  @IsNotEmpty()
  @IsString()
  @Length(2, 50, {
    message: 'Category name must be between 2 and 50 characters.',
  })
  @Transform(({ value }) => sanitize(value))
  name: string;

  @IsOptional()
  @IsBoolean()
  status?: boolean;

  @IsOptional()
  @IsBoolean()
  include_in_menu?: boolean;

  @IsOptional()
  @IsString()
  @Transform(({ value }) => sanitize(value))
  @Length(2, 50, {
    message: 'meta_title must be between 2 and 50 characters.',
  })
  meta_title?: string;

  @IsOptional()
  @IsString()
  @Length(2, 150, {
    message: 'meta_description must be between 2 and 150 characters.',
  })
  @Transform(({ value }) => sanitize(value))
  meta_description?: string;

  @IsOptional()
  @IsString()
  banner_web?: string;

  @IsOptional()
  @IsString()
  banner_app?: string;

  @IsOptional()
  @IsString()
  description?: string;

  // @IsOptional()
  // @IsString()
  // url_key: string;

  @IsOptional()
  @IsString()
  @Transform(({ value }) => sanitize(value))
  meta_keyword?: string;

  @IsOptional()
  @IsString()
  image?: string;

  @IsOptional()
  @IsString()
  path?: string;

  @IsOptional()
  @IsString()
  web_link?: string;

  @IsOptional()
  @IsString()
  banner_title?: string;

  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ProductPosition)
  category_products?: ProductPosition[];

  @IsOptional()
  @IsNumber()
  parent_id?: number;
}

export class ProductPosition {
  @IsNumber()
  product_id: number;

  @IsNumber()
  position: number;
}

export class UpdateCatalogCategoryDto {
  @IsOptional()
  @IsString()
  @Length(2, 50, {
    message: 'Category name must be between 2 and 50 characters.',
  })
  @Transform(({ value }) => sanitize(value))
  name?: string;

  @IsOptional()
  @IsBooleanString()
  status?: string;

  @IsOptional()
  @IsString()
  @Transform(({ value }) => sanitize(value))
  @Length(2, 50, {
    message: 'meta_title must be between 2 and 50 characters.',
  })
  meta_title?: string;

  @IsOptional()
  @IsString()
  @Length(2, 150, {
    message: 'meta_description must be between 2 and 150 characters.',
  })
  @Transform(({ value }) => sanitize(value))
  meta_description?: string;

  @IsOptional()
  @IsString()
  include_in_menu?: string;

  @IsOptional()
  @IsString()
  banner_web?: string;

  @IsOptional()
  @IsString()
  banner_app?: string;

  @IsOptional()
  @IsString()
  description?: string;

  @IsOptional()
  @IsString()
  url_key?: string;

  @IsOptional()
  @IsString()
  @Length(2, 255, {
    message: 'meta_keyword must be between 2 and 255 characters.',
  })
  @Transform(({ value }) => sanitize(value))
  meta_keyword?: string;

  @IsOptional()
  @IsString()
  image?: string;

  @IsOptional()
  @IsBooleanString()
  enableRedirect?: string;

  @IsOptional()
  @IsString()
  path?: string;

  @IsOptional()
  @IsString()
  parent_id?: string;

  @IsOptional()
  @IsString()
  web_link?: string;

  @IsOptional()
  @IsString()
  banner_title?: string;
}

export class AddProductToCategoryDto {
  @IsNotEmpty()
  @IsNumber()
  categoryId: number;

  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ProductPosition)
  product_ids: ProductPosition[];

  @IsOptional()
  @IsNumber()
  position: number;
}

export class removeProductsFromCategoryDto {
  @IsNotEmpty()
  @IsNumber()
  categoryId: number;

  @IsOptional()
  @IsArray()
  @Type(() => Number)
  product_ids: number[];

  @IsOptional()
  @IsNumber()
  position: number;
}
function sanitize(value: string): string {
  // Remove <script> tags and their content
  value = value.replace(/<script[\s\S]*?>[\s\S]*?<\/script>/gi, '');

  // Remove newlines and tabs
  value = value.replace(/[\n\t]/g, '');

  // Remove any other HTML tags
  value = value.replace(/<\/?[^>]+(>|$)/g, '');

  // Normalize whitespace: replace multiple spaces with a single space
  value = value.replace(/\s+/g, ' ');

  return value.trim();
}

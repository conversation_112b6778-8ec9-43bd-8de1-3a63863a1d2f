import { Module, forwardRef } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { CatalogCategoryService } from './catalog-category.service';
import { CatalogCategoryController } from './catalog-category.controller';
import { CatalogCategory } from 'src/database/entities/category/main-category.entity';
import { ProductCategoryRelation } from 'src/database/entities/category/product-category-relation.entity';
import { CategoryBanner } from 'src/database/entities/category/category-banner.entity';
import { CatalogProductModule } from '../catalog-product/catalog-product.module';
import { ProductAttributeName } from 'src/database/entities/product/product-attribute-name.entity';
import { CatalogProduct } from 'src/database/entities/product/main-product.entity';
import { S3Service } from 'src/utils/s3service';
import { UtilsModule } from 'src/utils/utils.module';
import { UrlRewrites } from 'src/database/entities/product/url-rewrites.entity';
import { BrandsModule } from '../brands/brands.module';
import { EventsOutbox } from 'src/database/entities/outbox/event-outbox.entity';
import { ActivityLogs } from 'src/database/entities/product/activity-logs.entity';
import { Activity } from 'src/database/entities/product/activity.entity';
import { LoggerService } from 'src/utils/logger-service';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      CatalogCategory,
      ProductCategoryRelation,
      CategoryBanner,
      ProductAttributeName,
      CatalogProduct,
      UrlRewrites,
      EventsOutbox,
      ActivityLogs,
      Activity,
    ]),
    CatalogProductModule,
    UtilsModule,
    BrandsModule,
    forwardRef(() => BrandsModule),
  ],
  providers: [CatalogCategoryService, LoggerService],
  controllers: [CatalogCategoryController],
  exports: [CatalogCategoryService],
})
export class CatalogCategoryModule {}

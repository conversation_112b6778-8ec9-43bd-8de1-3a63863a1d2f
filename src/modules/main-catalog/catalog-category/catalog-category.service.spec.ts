import { Test, TestingModule } from '@nestjs/testing';
import { BadRequestException, INestApplication } from '@nestjs/common';
import { CatalogCategoryService } from './catalog-category.service';
import { getRepositoryToken, TypeOrmModule } from '@nestjs/typeorm';
import { CatalogCategory } from 'src/database/entities/category/main-category.entity';
import { ProductCategoryRelation } from 'src/database/entities/category/product-category-relation.entity';
import { CatalogProduct } from 'src/database/entities/product/main-product.entity';
import { ProductAttributeName } from 'src/database/entities/product/product-attribute-name.entity';
import { UrlRewrites } from 'src/database/entities/product/url-rewrites.entity';
import { CatalogProductService } from '../catalog-product/catalog-product.service';
import { S3Service } from 'src/utils/s3service';
import { LoggerService } from 'src/utils/logger-service';
import { ExternalApiHelper } from 'src/utils/external-api-helper';
import { DataSource, EntityManager, In, Not, Repository } from 'typeorm';
import {
  CreateCatalogCategoryDto,
  UpdateCatalogCategoryDto,
} from './dto/catalog-category.dto';
import * as mysql from 'mysql2/promise';
import { AttributesGroup } from 'src/database/entities/product/attributes-group.entity';
import { AttributesGroupRelation } from 'src/database/entities/product/attributes-group-relation.entity';
import { ProductAttributesOptions } from 'src/database/entities/product/attribute-options.entity';
import { IntegerAttributeValues } from 'src/database/entities/product/integer-attribute-values.entity';
import { StringAttributeValues } from 'src/database/entities/product/string-attibute-values.entity';
import { TextAttributeValues } from 'src/database/entities/product/text-attribute-values.entity';
import { BooleanAttributeValues } from 'src/database/entities/product/boolean-attribute-values.entity';
import { DecimalAttributeValues } from 'src/database/entities/product/decimal-attribute-values.entity';
import { DateAttributeValues } from 'src/database/entities/product/date-attribute-values.entity';
import { TierPrices } from 'src/database/entities/product/tier-prices.entity';
import { InventoryAttributes } from 'src/database/entities/product/inventory-attributes-values.entity';
import { CatalogProductRelation } from 'src/database/entities/product/catalog-product-relation.entity';
import { MediaGallary } from 'src/database/entities/product/media-gallary.entity';
import { MediaGallaryVideo } from 'src/database/entities/product/media-gallary-video.entity';
import { CatalogProductFlat } from 'src/database/entities/product/main-product-flat.entity';
import { ProductDataCsvUrls } from 'src/database/entities/product/product-data-csv-urls.entity';
import { BulkAttributesUpdateStatus } from 'src/database/entities/product/bulk-attributes-update-status.entity';
import {
  Activity,
  EntityTypeEnum,
} from 'src/database/entities/product/activity.entity';
import { ActivityLogs } from 'src/database/entities/product/activity-logs.entity';
import { EventsOutbox } from 'src/database/entities/outbox/event-outbox.entity';
import { SkuUpdateRecord } from 'src/database/entities/product/sku-update-record.entity';
import { ProductStockAlert } from 'src/database/entities/product/product-stock-alert.entity';
import { QuestionAnswer } from 'src/database/entities/product/product-question-answer.entity';
import { LikeDislike } from 'src/database/entities/product/likes-dislikes.entity';
import { ProductFeedback } from 'src/database/entities/product/product-feedback.entity';
import { ProductsTag } from 'src/database/entities/product/product-tag.entity';
import { ProductsAttachment } from 'src/database/entities/product/product-attachment.entity';
import { EventsLogService } from 'src/utils/events-logging-service';
import { BrandsService } from '../brands/brands.service';
import { validate } from 'class-validator';
import { plainToInstance } from 'class-transformer';

jest.setTimeout(300000);

describe('CatalogCategoryService', () => {
  let service: CatalogCategoryService;
  let app: INestApplication;
  let testDBName: string;
  let categoryRepository: Repository<CatalogCategory>;
  let urlRewritesRepository: Repository<UrlRewrites>;
  let catalogProductRepository: Repository<CatalogProduct>;
  let productCategoryRelationRepository: Repository<ProductCategoryRelation>;
  let dataSource: DataSource;
  let entityManager: EntityManager;
  let connection: mysql.Connection;

  // Mock services
  const mockS3Service = {
    uploadFilesToS3: jest.fn(),
    deleteFiles: jest.fn(),
    uploadFileToS3: jest.fn(),
  };

  const mockLoggerService = {
    error: jest.fn(),
    log: jest.fn(),
  };

  const mockExternalApiHelper = {
    sendCategoryInMongoDbEventBus: jest.fn(),
    sendCategoryInElastisticSearchEventBus: jest.fn(),
    sendUrlRewritesInElastisticSearchEventBus: jest.fn(),
  };

  const LoggerFunction = {
    saveActivityAndEvent: jest.fn(),
    createOrUpdateLog: jest.fn(),
    createEventTableEntry: jest.fn(),
  };

  const BrandFunction = {
    updateBrandUrlKey: jest.fn(),
  };

  const mockCreateCategoryDto = plainToInstance(CreateCatalogCategoryDto, {
    name: 'Test Category',
    parent_id: 1,
    status: false,
    include_in_menu: false,
    meta_title: '',
    meta_description: '',
    banner_web: '',
    banner_app: '',
    description: '',
    meta_keyword: '',
    image: '',
    path: '',
    web_link: '',
    banner_title: '',
    category_products: [],
  });
  const headers = {};

  const urlRewrites: UrlRewrites = {
    id: 1,
    entity_id: 1,
    entity_type: EntityTypeEnum.CATEGORY,
    redirect_type: 0,
    request_path: 'test-category.html',
    target_path: '1',
    created_at: new Date(),
    updated_at: new Date(),
  };

  beforeAll(async () => {
    testDBName = 'test_cs_' + Date.now();

    // Create test database
    connection = await mysql.createConnection({
      host: process.env.TEST_DB_HOST || 'localhost',
      port: parseInt(process.env.TEST_DB_PORT || '3306'),
      user: process.env.TEST_DB_USERNAME || 'root',
      password: process.env.TEST_DB_PASSWORD || '',
    });

    await connection.query(`CREATE DATABASE IF NOT EXISTS ${testDBName}`);

    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [
        TypeOrmModule.forRoot({
          type: 'mysql',
          host: process.env.TEST_DB_HOST || 'localhost',
          port: parseInt(process.env.TEST_DB_PORT || '3306'),
          username: process.env.TEST_DB_USERNAME || 'root',
          password: process.env.TEST_DB_PASSWORD || '',
          database: testDBName,
          entities: [
            CatalogCategory,
            ProductCategoryRelation,
            CatalogProduct,
            ProductAttributeName,
            UrlRewrites,
            AttributesGroup,
            AttributesGroupRelation,
            ProductAttributesOptions,
            IntegerAttributeValues,
            StringAttributeValues,
            TextAttributeValues,
            BooleanAttributeValues,
            DecimalAttributeValues,
            DateAttributeValues,
            TierPrices,
            InventoryAttributes,
            CatalogProductRelation,
            MediaGallary,
            MediaGallaryVideo,
            CatalogProductFlat,
            ProductDataCsvUrls,
            BulkAttributesUpdateStatus,
            Activity,
            ActivityLogs,
            EventsOutbox,
            SkuUpdateRecord,
            ProductStockAlert,
            QuestionAnswer,
            LikeDislike,
            ProductFeedback,
            ProductsTag,
            ProductsAttachment,
          ],
          synchronize: true,
          dropSchema: true,
        }),
        TypeOrmModule.forFeature([
          CatalogCategory,
          ProductCategoryRelation,
          CatalogProduct,
          ProductAttributeName,
          UrlRewrites,
          AttributesGroup,
          AttributesGroupRelation,
          ProductAttributesOptions,
          IntegerAttributeValues,
          StringAttributeValues,
          TextAttributeValues,
          BooleanAttributeValues,
          DecimalAttributeValues,
          DateAttributeValues,
          TierPrices,
          InventoryAttributes,
          CatalogProductRelation,
          MediaGallary,
          MediaGallaryVideo,
          CatalogProductFlat,
          ProductDataCsvUrls,
          BulkAttributesUpdateStatus,
          Activity,
          ActivityLogs,
          EventsOutbox,
          SkuUpdateRecord,
          ProductStockAlert,
          QuestionAnswer,
          LikeDislike,
          ProductFeedback,
          ProductsTag,
          ProductsAttachment,
        ]),
      ],
      providers: [
        CatalogCategoryService,
        CatalogProductService,
        {
          provide: S3Service,
          useValue: mockS3Service,
        },
        {
          provide: LoggerService,
          useValue: mockLoggerService,
        },
        {
          provide: ExternalApiHelper,
          useValue: mockExternalApiHelper,
        },
        {
          provide: EventsLogService,
          useValue: LoggerFunction,
        },

        {
          provide: BrandsService,
          useValue: BrandFunction,
        },
      ],
    }).compile();

    app = moduleFixture.createNestApplication();
    await app.init();

    dataSource = moduleFixture.get<DataSource>(DataSource);
    entityManager = moduleFixture.get<EntityManager>(EntityManager);
    service = moduleFixture.get<CatalogCategoryService>(CatalogCategoryService);

    // Get all repositories
    categoryRepository = moduleFixture.get<Repository<CatalogCategory>>(
      getRepositoryToken(CatalogCategory),
    );

    urlRewritesRepository = moduleFixture.get<Repository<UrlRewrites>>(
      getRepositoryToken(UrlRewrites),
    );

    catalogProductRepository = moduleFixture.get<Repository<CatalogProduct>>(
      getRepositoryToken(CatalogProduct),
    );

    productCategoryRelationRepository = moduleFixture.get<
      Repository<ProductCategoryRelation>
    >(getRepositoryToken(ProductCategoryRelation));
  });

  afterAll(async () => {
    if (connection) {
      // Drop test database
      await connection.query(`DROP DATABASE IF EXISTS ${testDBName}`);
      await connection.end();
    }

    if (dataSource?.isInitialized) {
      await dataSource.destroy();
    }

    if (app) {
      await app.close();
    }
  });

  beforeEach(async () => {
    jest.clearAllMocks();
    mockCreateCategoryDto.name = 'Test Category';
    await entityManager.query('SET FOREIGN_KEY_CHECKS = 0;');
    await cleanDatabase();
    await categoryRepository.clear();
    await urlRewritesRepository.clear();
    await catalogProductRepository.clear();
    await productCategoryRelationRepository.clear();
    await entityManager.query('SET FOREIGN_KEY_CHECKS = 1;');

    jest
      .spyOn(mockExternalApiHelper, 'sendCategoryInMongoDbEventBus')
      .mockResolvedValue({ status: 200, data: { success: true } });

    jest
      .spyOn(mockExternalApiHelper, 'sendUrlRewritesInElastisticSearchEventBus')
      .mockResolvedValue({
        status: 200,
        data: { success: true },
      });
  });

  afterEach(async () => {
    // Clean up any pending transactions
    const connection = entityManager.connection;
    const queryRunner = connection.createQueryRunner();
    if (queryRunner.isTransactionActive) {
      await queryRunner.rollbackTransaction();
    }
    await queryRunner.release();
  });

  async function cleanDatabase() {
    const tables = await entityManager.query(
      `SELECT table_name 
       FROM information_schema.tables 
       WHERE table_schema = '${testDBName}';`,
    );

    for (const table of tables) {
      if (table.table_name) {
        await entityManager.query(`TRUNCATE TABLE \`${table.table_name}\`;`);
      }
    }
  }

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('createCatalogCategory', () => {
    describe('Category Creation Tests', () => {
      it('should successfully create a category', async () => {
        // Mock external api helper and return a successful response
        jest
          .spyOn(mockExternalApiHelper, 'sendCategoryInMongoDbEventBus')
          .mockResolvedValue({ status: 200, data: { success: true } });

        jest
          .spyOn(
            mockExternalApiHelper,
            'sendUrlRewritesInElastisticSearchEventBus',
          )
          .mockResolvedValue({
            status: 200,
            data: { success: true },
          });

        const result = await service.createCatalogCategory(
          mockCreateCategoryDto,
          headers,
        );

        expect(result).toBeDefined();
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        const { category_products, ...expectedCategory } =
          mockCreateCategoryDto;
        expect(result.savedCategory).toEqual({
          ...expectedCategory,
          created_at: expect.any(Date),
          updated_at: expect.any(Date),
          id: expect.any(Number),
          position: expect.any(Number),
          url_key: expect.stringMatching(/^.+$/),
        });
      });

      it('should throw an error if category already exists', async () => {
        try {
          await service.createCatalogCategory(mockCreateCategoryDto, headers);
          // try to create the same category again
          await service.createCatalogCategory(mockCreateCategoryDto, headers);
        } catch (error) {
          // check for bad request exception
          expect(error.status).toEqual(400);
          // Check if error message is correct
          expect(error.message).toEqual(
            'A category with the same name already exists for the same parent category',
          );
        }
      });

      it('should throw an error if category name is empty', async () => {
        try {
          mockCreateCategoryDto.name = '';
          await service.createCatalogCategory(mockCreateCategoryDto, headers);
        } catch (error) {
          // check for bad request exception
          expect(error.status).toEqual(400);
          // Check if error message is correct
          expect(error.message).toEqual(
            'Please add category name before creating a new category',
          );
        }
      });

      it('should throw an error if category name is null', async () => {
        try {
          mockCreateCategoryDto.name = null;
          await service.createCatalogCategory(mockCreateCategoryDto, headers);
        } catch (error) {
          // check for bad request exception
          expect(error.status).toEqual(400);
          // Check if error message is correct
          expect(error.message).toEqual(
            'Please add category name before creating a new category',
          );
        }
      });
    });

    describe('URL Key Generation Tests', () => {
      // Mock external api helper and return a successful response
      jest
        .spyOn(mockExternalApiHelper, 'sendCategoryInMongoDbEventBus')
        .mockResolvedValue({ status: 200, data: { success: true } });

      jest
        .spyOn(
          mockExternalApiHelper,
          'sendUrlRewritesInElastisticSearchEventBus',
        )
        .mockResolvedValue({
          status: 200,
          data: { success: true },
        });

      it('should convert spaces to hyphens in url_key', async () => {
        mockCreateCategoryDto.name = 'Test Category Name';
        const result = await service.createCatalogCategory(
          mockCreateCategoryDto,
          headers,
        );
        expect(result.savedCategory.url_key).toBe('test-category-name');
      });

      it('should handle special characters in url_key', async () => {
        mockCreateCategoryDto.name = 'Test & Category @ Name!';
        const result = await service.createCatalogCategory(
          mockCreateCategoryDto,
          headers,
        );
        expect(result.savedCategory.url_key).toBe('test-category-name');
      });

      it('should handle duplicate names by appending number', async () => {
        mockCreateCategoryDto.name = 'Test Category';
        await service.createCatalogCategory(mockCreateCategoryDto, headers);
        mockCreateCategoryDto.parent_id = 2;
        const result = await service.createCatalogCategory(
          mockCreateCategoryDto,
          {},
        );
        expect(result.savedCategory.url_key).toBe('test-category-1');
      });

      it('should trim leading and trailing spaces', async () => {
        mockCreateCategoryDto.name = '  Test Category  ';
        const result = await service.createCatalogCategory(
          mockCreateCategoryDto,
          headers,
        );
        expect(result.savedCategory.url_key).toBe('test-category');
      });

      it('should handle multiple consecutive spaces', async () => {
        mockCreateCategoryDto.name = 'Test    Category     Name';
        const result = await service.createCatalogCategory(
          mockCreateCategoryDto,
          headers,
        );
        expect(result.savedCategory.url_key).toBe('test-category-name');
      });
    });

    describe('Transaction Tests', () => {
      beforeEach(() => {
        // Reset mocks before each test
        jest.clearAllMocks();

        // Mock successful responses
        mockExternalApiHelper.sendCategoryInMongoDbEventBus.mockResolvedValue({
          status: 200,
          data: { success: true },
        });

        mockExternalApiHelper.sendUrlRewritesInElastisticSearchEventBus.mockResolvedValue(
          {
            status: 200,
            data: { success: true },
          },
        );
      });

      it('should rollback transaction if url_rewrites creation fails', async () => {
        // Force url rewrites to fail by creating a duplicate entry
        await urlRewritesRepository.save(urlRewrites);

        try {
          await service.createCatalogCategory(mockCreateCategoryDto, headers);
          fail('Should have thrown an error');
        } catch (error) {
          // Verify no category was created
          const categories = await categoryRepository.find();
          expect(categories.length).toBe(0);
        }
      });

      // it('should rollback transaction if MongoDB sync fails', async () => {
      //   // Mock MongoDB sync failure
      //   mockExternalApiHelper.sendCategoryInMongoDbEventBus.mockRejectedValue(
      //     new Error('MongoDB sync failed'),
      //   );

      //   try {
      //     await service.createCatalogCategory(mockCreateCategoryDto, headers);
      //     fail('Should have thrown an error');
      //   } catch (error) {
      //     // Verify the category and url_rewrites were rolled back
      //     const categories = await categoryRepository.find();
      //     const urlRewrites = await urlRewritesRepository.find();
      //     expect(categories.length).toBe(0);
      //     expect(urlRewrites.length).toBe(0);
      //   }
      // });

      it('should handle concurrent category creation with same name', async () => {
        // Attempt to create two categories with same name simultaneously
        const promises = [
          service.createCatalogCategory(mockCreateCategoryDto, headers),
          service.createCatalogCategory(mockCreateCategoryDto, headers),
        ];

        try {
          const results = await Promise.allSettled(promises);
          expect(results.filter((r) => r.status === 'fulfilled').length).toBe(
            1,
          );
          expect(results.filter((r) => r.status === 'rejected').length).toBe(1);
        } catch (error) {
          // Verify only one category was created
          const categories = await categoryRepository.find();
          expect(categories.length).toBe(1);
        } finally {
          const categories = await categoryRepository.find();
          // Verify only one category was created
          expect(categories.length).toBe(1);
        }
      });

      it('should maintain data consistency during parallel operations', async () => {
        // Create multiple categories in parallel with different names
        const categoryPromises = Array(5)
          .fill(null)
          .map((_, i) =>
            service.createCatalogCategory(
              {
                ...mockCreateCategoryDto,
                name: `Test Category ${i}`,
              },
              headers,
            ),
          );

        await Promise.all(categoryPromises);

        // Verify all categories were created correctly
        const categories = await categoryRepository.find();
        const urlRewrites = await urlRewritesRepository.find();

        expect(categories.length).toBe(5);
        expect(urlRewrites.length).toBe(5);

        // Verify each category has corresponding url_rewrites entry
        for (const category of categories) {
          const urlRewrite = urlRewrites.find(
            (u) => u.entity_id === category.id,
          );
          expect(urlRewrite).toBeDefined();
          expect(urlRewrite.request_path).toBe(`${category.url_key}.html`);
        }
      });

      // it('should handle transaction timeout', async () => {
      //   // Mock a slow MongoDB sync
      //   mockExternalApiHelper.sendCategoryInMongoDbEventBus.mockImplementation(
      //     async () => {
      //       await new Promise((resolve) => setTimeout(resolve, 5000)); // 5 second delay
      //       return { status: 200, data: { success: true } };
      //     },
      //   );

      //   try {
      //     await service.createCatalogCategory(mockCreateCategoryDto, headers);
      //     fail('Should have thrown a timeout error');
      //   } catch (error) {
      //     // Verify the transaction was rolled back
      //     const categories = await categoryRepository.find();
      //     const urlRewrites = await urlRewritesRepository.find();

      //     expect(categories.length).toBe(0);
      //     expect(urlRewrites.length).toBe(0);
      //   }
      // });
    });

    describe('Category Name Validation', () => {
      it('should validate max length of category name', async () => {
        mockCreateCategoryDto.name = 'a'.repeat(256); // Max length is 255

        const errors = await validate(mockCreateCategoryDto);

        console.log('CREATE ERROR', errors);
        // Assert validation errors
        expect(errors.length).toBeGreaterThan(0);
        expect(errors[0].constraints).toHaveProperty('isLength');
        expect(errors[0].constraints.isLength).toContain(
          'Category name must be between 2 and 50 characters.',
        );

        // fail('Should have thrown error for long name');
        // try {
        //   const errors = await validate(mockCreateCategoryDto);
        //   console.log('DTO VALIDATION ERROR', errors);
        //   await service.createCatalogCategory(mockCreateCategoryDto, headers);
        //   fail('Should have thrown error for long name');
        // } catch (error) {
        //   console.log('MY ERROR', error);
        //   expect(error.status).toBe(400); // current code throws 500 while it should be 400 semantically
        //   expect(error.message).toContain('Category name is too long');
        // }
      });

      it('should sanitize malicious category names', async () => {
        mockCreateCategoryDto.name =
          '<script>alert("xss")</script>Test Category';
        const transformedInstance = plainToInstance(
          CreateCatalogCategoryDto,
          mockCreateCategoryDto,
        );

        const errors = await validate(transformedInstance);

        const result = await service.createCatalogCategory(
          transformedInstance,
          headers,
        );
        expect(result.savedCategory.name).toBe('Test Category');
        expect(result.savedCategory.url_key).toBe('test-category');
      });

      it('should handle unicode characters in category name', async () => {
        mockCreateCategoryDto.name = 'Test Catégory 官话';
        const transformedInstance = plainToInstance(
          CreateCatalogCategoryDto,
          mockCreateCategoryDto,
        );

        const result = await service.createCatalogCategory(
          transformedInstance,
          headers,
        );
        expect(result.savedCategory.name).toBe('Test Catégory 官话');
        expect(result.savedCategory.url_key).toBe('test-category');
      });

      it('should normalize whitespace in category name', async () => {
        mockCreateCategoryDto.name = ' Test    Category\t\n  Name ';
        const transformedInstance = plainToInstance(
          CreateCatalogCategoryDto,
          mockCreateCategoryDto,
        );

        const result = await service.createCatalogCategory(
          transformedInstance,
          headers,
        );

        expect(result.savedCategory.name).toBe('Test Category Name');
      });
    });

    describe('Parent Category Validation', () => {
      it('should validate parent category exists', async () => {
        mockCreateCategoryDto.parent_id = 99999; // Non-existent parent

        try {
          await service.createCatalogCategory(mockCreateCategoryDto, headers);
          fail('Should have thrown error for invalid parent');
        } catch (error) {
          expect(error.status).toBe(400);
          expect(error.message).toContain('Parent category does not exist');
        }
      });

      it('should default to root category if parent_id is null', async () => {
        mockCreateCategoryDto.parent_id = null;
        const result = await service.createCatalogCategory(
          mockCreateCategoryDto,
          headers,
        );
        expect(result.savedCategory.parent_id).toBe(1); // Root category ID
      });
    });

    describe('Meta Data Validation', () => {
      it('should validate meta title length', async () => {
        mockCreateCategoryDto.meta_title = 'a'.repeat(256);

        const transformedInstance = plainToInstance(
          CreateCatalogCategoryDto,
          mockCreateCategoryDto,
        );

        const errors = await validate(transformedInstance);

        console.log({ mockCreateCategoryDto, errors });
        // Assert validation errors
        expect(errors.length).toBeGreaterThan(0);
        expect(errors[0].constraints).toHaveProperty('isLength');
        expect(errors[0].constraints.isLength).toContain(
          'meta_title must be between 2 and 50 characters.',
        );
      });

      it('should validate meta description length', async () => {
        mockCreateCategoryDto.meta_title = undefined;
        mockCreateCategoryDto.meta_description = 'a'.repeat(1001);

        const errors = await validate(mockCreateCategoryDto);

        // Assert validation errors
        expect(errors.length).toBeGreaterThan(0);
        expect(errors[0].constraints).toHaveProperty('isLength');
        expect(errors[0].constraints.isLength).toContain(
          'meta_description must be between 2 and 150 characters.',
        );
      });

      it('should sanitize meta data fields', async () => {
        const transformedInstance = plainToInstance(CreateCatalogCategoryDto, {
          ...mockCreateCategoryDto,
          meta_title: '<script>alert("xss")</script>Meta Title',
          meta_description: '<script>alert("xss")</script>Meta Description',
          meta_keyword: '<script>alert("xss")</script>keyword',
        });

        const category = await service.createCatalogCategory(
          transformedInstance,
          headers,
        );

        expect(category.savedCategory.meta_title).toBe('Meta Title');
        expect(category.savedCategory.meta_description).toBe(
          'Meta Description',
        );
        expect(category.savedCategory.meta_keyword).toBe('keyword');
      });
    });
  });

  describe('updateCatalogCategory', () => {
    const testCategory = {
      id: 1,
      name: 'Test Category',
      status: false,
      include_in_menu: false,
      parent_id: 1,
      url_key: 'test-category',
      created_at: new Date(),
      updated_at: new Date(),
      meta_title: 'Test Meta Title',
      position: 1,
    };

    beforeEach(async () => {
      await categoryRepository.save(testCategory);
    });

    describe('Basic Update Functionality', () => {
      it('should successfully update basic category details', async () => {
        const updateDto = {
          name: 'Updated Category',
          status: 'true',
          include_in_menu: 'true',
        };

        const result = await service.updateCategory(
          updateDto,
          undefined,
          testCategory.id,
          headers,
        );

        expect(result).toBeDefined();
        expect(result.message).toBe('Category updated successfuly');
        expect(result.categoryData).toMatchObject({
          name: 'Updated Category',
          status: true,
          include_in_menu: true,
        });
      });

      it('should throw error when category ID does not exist', async () => {
        const updateDto: UpdateCatalogCategoryDto = {
          name: 'Updated Category',
        };
        const files = [];
        const nonExistentId = 99999;

        await expect(
          service.updateCategory(updateDto, undefined, nonExistentId, headers),
        ).rejects.toThrow('Requested Category does not exists');
      });

      it('should throw error when updating category with empty name', async () => {
        const updateDto: UpdateCatalogCategoryDto = {
          name: '',
        };

        const transformedInstance = plainToInstance(
          UpdateCatalogCategoryDto,
          updateDto,
        );

        const errors = await validate(transformedInstance);

        // Assert validation errors
        expect(errors.length).toBeGreaterThan(0);
        expect(errors[0].constraints).toHaveProperty('isLength');
        expect(errors[0].constraints.isLength).toContain(
          'Category name must be between 2 and 50 characters.',
        );
      });

      it('should throw error when updating to duplicate name under same parent', async () => {
        // First create another category
        await categoryRepository.save({
          ...testCategory,
          id: 2,
          name: 'Existing Category',
          url_key: 'existing-category',
        });

        const updateDto: UpdateCatalogCategoryDto = {
          name: 'Existing Category',
        };
        const files = [];

        await expect(
          service.updateCategory(
            updateDto,
            undefined,
            testCategory.id,
            headers,
          ),
        ).rejects.toThrow(
          'A category with the same name already exists for the same parent category',
        );
      });

      it('should update category status correctly', async () => {
        const updateDto: UpdateCatalogCategoryDto = {
          status: 'true',
        };

        const result = await service.updateCategory(
          updateDto,
          undefined,
          testCategory.id,
          headers,
        );

        expect(result.categoryData.status).toBe(true);
      });

      it('should update include_in_menu correctly', async () => {
        const updateDto: UpdateCatalogCategoryDto = {
          include_in_menu: 'true',
        };

        const result = await service.updateCategory(
          updateDto,
          undefined,
          testCategory.id,
          headers,
        );

        expect(result.categoryData.include_in_menu).toBe(true);
      });

      it('should successfully update only provided fields', async () => {
        // Initial state
        const originalCategory = await categoryRepository.findOne({
          where: { id: testCategory.id },
        });

        // Update with only some fields
        const updateDto: UpdateCatalogCategoryDto = {
          name: 'Updated Category',
          status: 'true',
        };

        const result = await service.updateCategory(
          updateDto,
          undefined,
          testCategory.id,
          headers,
        );

        // Verify only specified fields were updated
        expect(result.categoryData).toMatchObject({
          id: testCategory.id,
          name: 'Updated Category',
          status: true,
          // Other fields should match original values
          include_in_menu: originalCategory.include_in_menu,
          meta_title: originalCategory.meta_title,
        });

        // Verify DTO transformation worked correctly
        expect(updateDto).not.toHaveProperty('meta_title');
        expect(updateDto).not.toHaveProperty('include_in_menu');
      });
    });

    describe('URL Key Update Tests', () => {
      let testUrlRewrites: UrlRewrites;

      beforeEach(async () => {
        // Create test URL rewrite entry
        testUrlRewrites = await urlRewritesRepository.save({
          entity_id: testCategory.id,
          entity_type: EntityTypeEnum.CATEGORY,
          request_path: 'test-category.html',
          target_path: String(testCategory.id),
          redirect_type: 0,
        });
      });

      it('should successfully update url_key', async () => {
        const updateDto: UpdateCatalogCategoryDto = {
          url_key: 'new-url-key',
        };

        const result = await service.updateCategory(
          updateDto,
          undefined,
          testCategory.id,
          headers,
        );

        // Check category was updated
        expect(result.categoryData.url_key).toBe('new-url-key');

        // Check URL rewrites were updated
        const newUrlRewrite = await urlRewritesRepository.findOne({
          where: {
            entity_id: testCategory.id,
            request_path: 'new-url-key.html',
          },
        });
        expect(newUrlRewrite).toBeDefined();
      });

      it('should set up redirect when enableRedirect is true', async () => {
        const updateDto: UpdateCatalogCategoryDto = {
          url_key: 'new-url-key-test',
          enableRedirect: 'true',
        };
        console.log('input', updateDto);

        await service.updateCategory(
          updateDto,
          undefined,
          testCategory.id,
          headers,
        );

        // Check old URL rewrite was updated to redirect
        const oldUrlRewrite = await urlRewritesRepository.findOne({
          where: {
            request_path: 'test-category.html',
          },
        });

        expect(oldUrlRewrite).toBeDefined();
        expect(oldUrlRewrite.redirect_type).toBe(301);
        expect(oldUrlRewrite.target_path).toBe('new-url-key-test.html');

        // Check new URL rewrite was created
        const newUrlRewrite = await urlRewritesRepository.findOne({
          where: {
            request_path: 'new-url-key-test.html',
          },
        });
        expect(newUrlRewrite).toBeDefined();
        expect(newUrlRewrite.redirect_type).toBe(0);
        expect(newUrlRewrite.target_path).toBe(String(testCategory.id));
      });

      it('should not set up redirect when enableRedirect is false', async () => {
        const updateDto: UpdateCatalogCategoryDto = {
          url_key: 'new-url-key',
          enableRedirect: 'false',
        };

        await service.updateCategory(
          updateDto,
          undefined,
          testCategory.id,
          headers,
        );

        // Old URL rewrite should be gone
        const oldUrlRewrite = await urlRewritesRepository.findOne({
          where: {
            request_path: 'test-category.html',
          },
        });
        expect(oldUrlRewrite).not.toBeNull();

        // Only new URL rewrite should exist
        const newUrlRewrite = await urlRewritesRepository.findOne({
          where: {
            request_path: 'new-url-key.html',
          },
        });
        expect(newUrlRewrite).toBeDefined();
        expect(newUrlRewrite.redirect_type).toBe(0);
      });

      it('should throw error for duplicate url_key', async () => {
        // Create another category with url_key
        await urlRewritesRepository.save({
          entity_id: 999,
          entity_type: EntityTypeEnum.CATEGORY,
          request_path: 'existing-url.html',
          target_path: '999',
          redirect_type: 0,
        });

        const updateDto: UpdateCatalogCategoryDto = {
          url_key: 'existing-url',
        };

        await expect(
          service.updateCategory(
            updateDto,
            undefined,
            testCategory.id,
            headers,
          ),
        ).rejects.toThrow('Url key must be unique');
      });

      it('should sanitize and format url_key value', async () => {
        const updateDto: UpdateCatalogCategoryDto = {
          url_key: ' Test URL Key! @#$ ',
        };

        const result = await service.updateCategory(
          updateDto,
          undefined,
          testCategory.id,
          headers,
        );

        expect(result.categoryData.url_key).toBe('test-url-key');
      });

      // it('should sync URL rewrites with ElasticSearch when url_key is updated', async () => {
      //   const updateDto: UpdateCatalogCategoryDto = {
      //     url_key: 'new-url-key',
      //     enableRedirect: 'true',
      //   };

      //   await service.updateCategory(updateDto, [], testCategory.id, headers);

      //   // Should sync both old and new URLs when redirect is enabled
      //   expect(
      //     mockExternalApiHelper.sendUrlRewritesInElastisticSearchEventBus,
      //   ).toHaveBeenCalledTimes(2);
      //   expect(
      //     mockExternalApiHelper.sendUrlRewritesInElastisticSearchEventBus,
      //   ).toHaveBeenCalledWith(
      //     expect.objectContaining({
      //       request_path: 'test-category.html',
      //     }),
      //   );
      //   expect(
      //     mockExternalApiHelper.sendUrlRewritesInElastisticSearchEventBus,
      //   ).toHaveBeenCalledWith(
      //     expect.objectContaining({
      //       request_path: 'new-url-key.html',
      //     }),
      //   );
      // });

      it('should handle empty spaces in url_key', async () => {
        const updateDto: UpdateCatalogCategoryDto = {
          url_key: '  new     url    key  ',
        };

        const result = await service.updateCategory(
          updateDto,
          undefined,
          testCategory.id,
          headers,
        );

        expect(result.categoryData.url_key).toBe('new-url-key');
      });

      it('should maintain url_key if not provided in update', async () => {
        const updateDto: UpdateCatalogCategoryDto = {
          name: 'Updated Name',
        };

        const result = await service.updateCategory(
          updateDto,
          undefined,
          testCategory.id,
          headers,
        );

        expect(result.categoryData.url_key).toBe('test-category');

        // URL rewrites should remain unchanged
        const urlRewrites = await urlRewritesRepository.find({
          where: { entity_id: testCategory.id },
        });
        expect(urlRewrites).toHaveLength(1);
        expect(urlRewrites[0].request_path).toBe('test-category.html');
      });
    });

    describe('Transaction Integrity Tests', () => {
      beforeEach(async () => {
        // Create initial test URL rewrite
        await urlRewritesRepository.save({
          entity_id: testCategory.id,
          entity_type: EntityTypeEnum.CATEGORY,
          request_path: 'test-category.html',
          target_path: String(testCategory.id),
          redirect_type: 0,
        });
      });

      // it('should rollback all changes if MongoDB sync fails', async () => {
      //   // mockExternalApiHelper.sendCategoryInMongoDbEventBus.mockRejectedValueOnce(
      //   //   new Error('MongoDB sync failed'),
      //   // );

      //   const updateDto: UpdateCatalogCategoryDto = {
      //     name: 'Updated Name',
      //     url_key: 'new-url-key',
      //     status: 'true',
      //   };

      //   await expect(
      //     service.updateCategory(updateDto, [], testCategory.id, headers),
      //   ).rejects.toThrow();

      //   // Verify category wasn't updated
      //   const category = await categoryRepository.findOne({
      //     where: { id: testCategory.id },
      //   });
      //   expect(category).toEqual(testCategory);

      //   // Verify URL rewrites weren't changed
      //   const urlRewrites = await urlRewritesRepository.find({
      //     where: { entity_id: testCategory.id },
      //   });
      //   expect(urlRewrites).toHaveLength(1);
      //   expect(urlRewrites[0].request_path).toBe('test-category.html');
      // });

      it('should rollback if URL rewrites update fails', async () => {
        // Force URL rewrite creation to fail
        await urlRewritesRepository.save({
          entity_id: 999,
          entity_type: EntityTypeEnum.CATEGORY,
          request_path: 'new-url-key.html',
          target_path: '999',
          redirect_type: 0,
        });

        const updateDto: UpdateCatalogCategoryDto = {
          name: 'Updated Name',
          url_key: 'new-url-key',
        };

        await expect(
          service.updateCategory(
            updateDto,
            undefined,
            testCategory.id,
            headers,
          ),
        ).rejects.toThrow();

        // Verify category wasn't updated
        const category = await categoryRepository.findOne({
          where: { id: testCategory.id },
        });
        expect(category).toEqual(testCategory);

        // Verify original URL rewrite remains unchanged
        const urlRewrites = await urlRewritesRepository.find({
          where: { entity_id: testCategory.id },
        });
        expect(urlRewrites).toHaveLength(1);
        expect(urlRewrites[0].request_path).toBe('test-category.html');
      });

      it('should handle concurrent updates to same category', async () => {
        const updateDto1: UpdateCatalogCategoryDto = {
          name: 'Update 1',
        };

        const updateDto2: UpdateCatalogCategoryDto = {
          name: 'Update 2',
        };

        // Try to update the same category concurrently
        const results = await Promise.allSettled([
          service.updateCategory(
            updateDto1,
            undefined,
            testCategory.id,
            headers,
          ),
          service.updateCategory(
            updateDto2,
            undefined,
            testCategory.id,
            headers,
          ),
        ]);

        // One update should succeed, one should fail
        expect(results.filter((r) => r.status === 'fulfilled')).toHaveLength(1);
        expect(results.filter((r) => r.status === 'rejected')).toHaveLength(1);

        // Verify only one update was applied
        const category = await categoryRepository.findOne({
          where: { id: testCategory.id },
        });
        expect(['Update 1', 'Update 2']).toContain(category.name);
      });

      it('should maintain referential integrity with URL rewrites', async () => {
        // Create multiple URL rewrites for testing
        let updateDto: UpdateCatalogCategoryDto = {
          url_key: 'new-url-1',
          enableRedirect: 'true',
        };
        await service.updateCategory(
          updateDto,
          undefined,
          testCategory.id,
          headers,
        );

        updateDto = {
          url_key: 'new-url-2',
          enableRedirect: 'true',
        };
        await service.updateCategory(
          updateDto,
          undefined,
          testCategory.id,
          headers,
        );

        updateDto = {
          url_key: 'final-url',
          enableRedirect: 'true',
        };
        await service.updateCategory(
          updateDto,
          undefined,
          testCategory.id,
          headers,
        );

        // Verify all redirects were updated to point to new URL
        const urlRewrites = await urlRewritesRepository.find({
          where: { entity_id: testCategory.id },
        });

        const redirects = urlRewrites.filter((u) => u.redirect_type === 301);
        redirects.forEach((redirect) => {
          expect(redirect.target_path).toBe('final-url.html');
        });
      });

      it('should handle errors during image file operations', async () => {
        // Mock S3 service to fail
        mockS3Service.uploadFilesToS3.mockRejectedValueOnce(
          new Error('S3 upload failed'),
        );

        const updateDto: UpdateCatalogCategoryDto = {
          name: 'Updated Name',
        };
        const files = [
          {
            originalname: 'test.jpg',
            buffer: Buffer.from('test'),
          } as Express.Multer.File,
        ];

        await expect(
          service.updateCategory(updateDto, files[0], testCategory.id, headers),
        ).rejects.toThrow();

        // Verify category wasn't updated
        const category = await categoryRepository.findOne({
          where: { id: testCategory.id },
        });
        expect(category).toEqual(testCategory);
      });
    });

    describe('Update or Move Category Tests', () => {
      let parentCategory: CatalogCategory;
      let siblingCategory1: CatalogCategory;
      let siblingCategory2: CatalogCategory;

      beforeEach(async () => {
        // Create a test category hierarchy
        parentCategory = await categoryRepository.save({
          name: 'Parent Category',
          status: false,
          include_in_menu: false,
          parent_id: 1,
          url_key: 'parent-category',
          position: 1,
        });

        // Create test categories under parent
        testCategory.parent_id = parentCategory.id;
        testCategory.position = 1;
        await categoryRepository.save(testCategory);

        siblingCategory1 = await categoryRepository.save({
          name: 'Sibling Category 1',
          status: false,
          include_in_menu: false,
          parent_id: parentCategory.id,
          url_key: 'sibling-category-1',
          position: 2,
        });

        siblingCategory2 = await categoryRepository.save({
          name: 'Sibling Category 2',
          status: false,
          include_in_menu: false,
          parent_id: parentCategory.id,
          url_key: 'sibling-category-2',
          position: 3,
        });

        const categories = await categoryRepository.find({
          where: { parent_id: parentCategory.id },
          order: { position: 'ASC' },
        });

        // Verify positions were updated correctly
        expect(categories[0].id).toBe(testCategory.id);
        expect(categories[0].position).toBe(1);
        expect(categories[1].id).toBe(siblingCategory1.id);
        expect(categories[1].position).toBe(2);
        expect(categories[2].id).toBe(siblingCategory2.id);
        expect(categories[2].position).toBe(3);
      });

      describe('Position Updates Under Same Parent', () => {
        it('should move category to a new position under same parent', async () => {
          await service.updateOrMoveCategory(
            testCategory.id,
            testCategory.parent_id,
            3,
            headers,
          );

          const categories = await categoryRepository.find({
            where: { parent_id: parentCategory.id },
            order: { position: 'ASC' },
          });

          // Verify positions were updated correctly
          expect(categories[0].id).toBe(siblingCategory1.id);
          expect(categories[0].position).toBe(1);
          expect(categories[1].id).toBe(siblingCategory2.id);
          expect(categories[1].position).toBe(2);
          expect(categories[2].id).toBe(testCategory.id);
          expect(categories[2].position).toBe(3);
        });

        it('should handle moving category to earlier position', async () => {
          // Move siblingCategory2 to position 1
          await service.updateOrMoveCategory(
            siblingCategory2.id,
            parentCategory.id,
            1,
            headers,
          );

          const categories = await categoryRepository.find({
            where: { parent_id: parentCategory.id },
            order: { position: 'ASC' },
          });

          expect(categories[0].id).toBe(siblingCategory2.id);
          expect(categories[0].position).toBe(1);
          expect(categories[1].id).toBe(testCategory.id);
          expect(categories[1].position).toBe(2);
          expect(categories[2].id).toBe(siblingCategory1.id);
          expect(categories[2].position).toBe(3);
        });

        it('should throw error for invalid position', async () => {
          await expect(
            service.updateOrMoveCategory(
              testCategory.id,
              parentCategory.id,
              99,
              headers,
            ),
          ).rejects.toThrow('Invalid new position');
        });
      });

      describe('Moving Category to New Parent', () => {
        let newParentCategory: CatalogCategory;

        beforeEach(async () => {
          newParentCategory = await categoryRepository.save({
            name: 'New Parent Category',
            status: false,
            include_in_menu: false,
            parent_id: 1,
            url_key: 'new-parent-category',
            position: 2,
          });
        });

        it('should move category to new parent', async () => {
          await service.updateOrMoveCategory(
            testCategory.id,
            newParentCategory.id,
            1,
            headers,
          );

          // Verify category moved to new parent
          const updatedCategory = await categoryRepository.findOne({
            where: { id: testCategory.id },
          });
          expect(updatedCategory.parent_id).toBe(newParentCategory.id);
          expect(updatedCategory.position).toBe(1);

          // Verify old siblings positions adjusted
          const oldSiblings = await categoryRepository.find({
            where: { parent_id: parentCategory.id },
            order: { position: 'ASC' },
          });
          expect(oldSiblings[0].position).toBe(1);
          expect(oldSiblings[1].position).toBe(2);
        });

        it('should position category at end of new parent when no position specified', async () => {
          // Add existing category under new parent
          await categoryRepository.save({
            name: 'Existing Under New Parent',
            parent_id: newParentCategory.id,
            position: 1,
            url_key: 'existing-new-parent',
          });

          const result = await service.updateOrMoveCategory(
            testCategory.id,
            newParentCategory.id,
            null,
            headers,
          );

          const categories = await categoryRepository.find({
            where: { parent_id: newParentCategory.id },
            order: { position: 'ASC' },
          });

          expect(categories).toHaveLength(2);
          expect(categories[1].id).toBe(testCategory.id);
          expect(categories[1].position).toBe(2);
        });
      });

      // describe('Event Bus Integration', () => {
      //   it('should send position updates to event bus', async () => {
      //     await service.updateOrMoveCategory(
      //       testCategory.id,
      //       parentCategory.id,
      //       3,
      //       headers,
      //     );

      //     // Verify categoryChangesArray was sent to event bus
      //     // const eventBusCalls =
      //     //   mockExternalApiHelper.sendCategoryInMongoDbEventBus.mock.calls;
      //     // expect(eventBusCalls).toHaveLength(3); // All affected categories

      //     // Verify the changed categories were included
      //     const updatedCategoryIds = eventBusCalls.map(
      //       (call) => call[0].entity_id,
      //     );
      //     expect(updatedCategoryIds).toContain(testCategory.id);
      //     expect(updatedCategoryIds).toContain(siblingCategory1.id);
      //     expect(updatedCategoryIds).toContain(siblingCategory2.id);
      //   });
      // });

      describe('Transaction Integrity', () => {
        // it('should rollback all position changes if sync fails', async () => {
        //   mockExternalApiHelper.sendCategoryInMongoDbEventBus.mockRejectedValueOnce(
        //     new Error('Sync failed'),
        //   );

        //   const originalPositions = await categoryRepository.find({
        //     where: { parent_id: parentCategory.id },
        //     order: { position: 'ASC' },
        //   });

        //   await expect(
        //     service.updateOrMoveCategory(
        //       testCategory.id,
        //       parentCategory.id,
        //       3,
        //       headers,
        //     ),
        //   ).rejects.toThrow();

        //   const currentPositions = await categoryRepository.find({
        //     where: { parent_id: parentCategory.id },
        //     order: { position: 'ASC' },
        //   });

        //   // Verify positions remained unchanged
        //   expect(currentPositions).toEqual(originalPositions);
        // });

        it('should handle concurrent position updates', async () => {
          const updatePromises = [
            service.updateOrMoveCategory(
              testCategory.id,
              parentCategory.id,
              3,
              headers,
            ),
            service.updateOrMoveCategory(
              siblingCategory1.id,
              parentCategory.id,
              3,
              headers,
            ),
          ];

          const results = await Promise.allSettled(updatePromises);

          // One should succeed, one should fail
          expect(results.filter((r) => r.status === 'fulfilled')).toHaveLength(
            1,
          );
          expect(results.filter((r) => r.status === 'rejected')).toHaveLength(
            1,
          );

          // Verify final positions are consistent
          const categories = await categoryRepository.find({
            where: { parent_id: parentCategory.id },
            order: { position: 'ASC' },
          });

          const positions = categories.map((c) => c.position);
          expect(new Set(positions).size).toBe(positions.length); // No duplicate positions
          expect(Math.min(...positions)).toBe(1); // Positions start at 1
          expect(Math.max(...positions)).toBe(categories.length); // No gaps
        });
      });

      describe('Position Edge Cases', () => {
        let parentCategory: CatalogCategory;
        let categories: CatalogCategory[] = [];

        beforeEach(async () => {
          // Create parent category
          parentCategory = await categoryRepository.save({
            name: 'Parent Category',
            status: false,
            include_in_menu: false,
            parent_id: 1,
            url_key: 'parent-category',
            position: 1,
          });

          // Create multiple categories under parent for complex position testing
          for (let i = 1; i <= 5; i++) {
            const category = await categoryRepository.save({
              name: `Category ${i}`,
              status: false,
              include_in_menu: false,
              parent_id: parentCategory.id,
              url_key: `category-${i}`,
              position: i,
            });
            categories.push(category);
          }

          jest.clearAllMocks();
        });

        describe('Complex Position Movements', () => {
          it('should handle moving first position to last', async () => {
            const firstCategory = categories[0];
            const lastPosition = categories.length;

            await service.updateOrMoveCategory(
              firstCategory.id,
              parentCategory.id,
              lastPosition,
              headers,
            );

            const updatedCategories = await categoryRepository.find({
              where: { parent_id: parentCategory.id },
              order: { position: 'ASC' },
            });

            // Verify all positions shifted up
            for (let i = 0; i < updatedCategories.length - 1; i++) {
              expect(updatedCategories[i].position).toBe(i + 1);
            }
            expect(updatedCategories[updatedCategories.length - 1].id).toBe(
              firstCategory.id,
            );
            expect(
              updatedCategories[updatedCategories.length - 1].position,
            ).toBe(lastPosition);
          });

          it('should handle moving last position to first', async () => {
            const lastCategory = categories[categories.length - 1];

            await service.updateOrMoveCategory(
              lastCategory.id,
              parentCategory.id,
              1,
              headers,
            );

            const updatedCategories = await categoryRepository.find({
              where: { parent_id: parentCategory.id },
              order: { position: 'ASC' },
            });

            expect(updatedCategories[0].id).toBe(lastCategory.id);
            expect(updatedCategories[0].position).toBe(1);

            // Verify all other positions shifted down
            for (let i = 1; i < updatedCategories.length; i++) {
              expect(updatedCategories[i].position).toBe(i + 1);
            }
          });

          it('should handle moving middle position down', async () => {
            const middleCategory = categories[2]; // Position 3
            const newPosition = 4;

            await service.updateOrMoveCategory(
              middleCategory.id,
              parentCategory.id,
              newPosition,
              headers,
            );

            const updatedCategories = await categoryRepository.find({
              where: { parent_id: parentCategory.id },
              order: { position: 'ASC' },
            });

            // Categories before middle should remain unchanged
            expect(updatedCategories[0].position).toBe(1);
            expect(updatedCategories[1].position).toBe(2);

            // Categories between old and new position should shift up
            expect(updatedCategories[2].position).toBe(3);

            // Moved category should be in new position
            expect(updatedCategories[3].id).toBe(middleCategory.id);
            expect(updatedCategories[3].position).toBe(4);
          });

          it('should handle moving middle position up', async () => {
            const middleCategory = categories[2]; // Position 3
            const newPosition = 2;

            await service.updateOrMoveCategory(
              middleCategory.id,
              parentCategory.id,
              newPosition,
              headers,
            );

            const updatedCategories = await categoryRepository.find({
              where: { parent_id: parentCategory.id },
              order: { position: 'ASC' },
            });

            // First position should remain unchanged
            expect(updatedCategories[0].position).toBe(1);

            // Moved category should be in new position
            expect(updatedCategories[1].id).toBe(middleCategory.id);
            expect(updatedCategories[1].position).toBe(2);

            // Categories between new and old position should shift down
            expect(updatedCategories[2].position).toBe(3);
          });
        });

        describe('Parent Change Position Edge Cases', () => {
          let newParentCategory: CatalogCategory;
          let existingCategoriesInNewParent: CatalogCategory[] = [];

          beforeEach(async () => {
            newParentCategory = await categoryRepository.save({
              name: 'New Parent Category',
              status: false,
              include_in_menu: false,
              parent_id: 1,
              url_key: 'new-parent-category',
              position: 2,
            });

            // Create existing categories under new parent
            for (let i = 1; i <= 3; i++) {
              const category = await categoryRepository.save({
                name: `New Parent Category ${i}`,
                status: false,
                include_in_menu: false,
                parent_id: newParentCategory.id,
                url_key: `new-parent-category-${i}`,
                position: i,
              });
              existingCategoriesInNewParent.push(category);
            }
          });

          it('should handle moving to middle position in new parent', async () => {
            const categoryToMove = categories[0];
            const newPosition = 2;

            await service.updateOrMoveCategory(
              categoryToMove.id,
              newParentCategory.id,
              newPosition,
              headers,
            );

            // Check positions in old parent
            const oldParentCategories = await categoryRepository.find({
              where: { parent_id: parentCategory.id },
              order: { position: 'ASC' },
            });

            // Verify positions in old parent adjusted correctly
            for (let i = 0; i < oldParentCategories.length; i++) {
              expect(oldParentCategories[i].position).toBe(i + 1);
            }

            // Check positions in new parent
            const newParentCategories = await categoryRepository.find({
              where: { parent_id: newParentCategory.id },
              order: { position: 'ASC' },
            });

            expect(newParentCategories[0].position).toBe(1);
            expect(newParentCategories[1].id).toBe(categoryToMove.id);
            expect(newParentCategories[1].position).toBe(2);
            expect(newParentCategories[2].position).toBe(3);
            expect(newParentCategories[3].position).toBe(4);
          });

          it('should handle moving multiple categories to same parent sequentially', async () => {
            // Move two categories to new parent one after another
            await service.updateOrMoveCategory(
              categories[0].id,
              newParentCategory.id,
              1,
              headers,
            );

            await service.updateOrMoveCategory(
              categories[1].id,
              newParentCategory.id,
              2,
              headers,
            );

            // Check final positions in both parents
            const oldParentCategories = await categoryRepository.find({
              where: { parent_id: parentCategory.id },
              order: { position: 'ASC' },
            });

            const newParentCategories = await categoryRepository.find({
              where: { parent_id: newParentCategory.id },
              order: { position: 'ASC' },
            });

            // Verify old parent positions are continuous
            for (let i = 0; i < oldParentCategories.length; i++) {
              expect(oldParentCategories[i].position).toBe(i + 1);
            }

            // Verify new parent positions are continuous
            for (let i = 0; i < newParentCategories.length; i++) {
              expect(newParentCategories[i].position).toBe(i + 1);
            }
          });

          it('should handle swapping positions between two parents', async () => {
            const category1 = categories[0];
            const category2 = existingCategoriesInNewParent[0];

            // Swap positions of categories between parents
            await service.updateOrMoveCategory(
              category1.id,
              newParentCategory.id,
              1,
              headers,
            );

            await service.updateOrMoveCategory(
              category2.id,
              parentCategory.id,
              1,
              headers,
            );

            const parent1Categories = await categoryRepository.find({
              where: { parent_id: parentCategory.id },
              order: { position: 'ASC' },
            });

            const parent2Categories = await categoryRepository.find({
              where: { parent_id: newParentCategory.id },
              order: { position: 'ASC' },
            });

            // Verify positions in both parents are continuous
            expect(parent1Categories[0].id).toBe(category2.id);
            expect(parent2Categories[0].id).toBe(category1.id);

            parent1Categories.forEach((cat, idx) => {
              expect(cat.position).toBe(idx + 1);
            });

            parent2Categories.forEach((cat, idx) => {
              expect(cat.position).toBe(idx + 1);
            });
          });
        });

        describe('Position Validation Edge Cases', () => {
          it('should handle same position move (no-op)', async () => {
            const category = categories[2];
            const currentPosition = category.position;

            await service.updateOrMoveCategory(
              category.id,
              parentCategory.id,
              currentPosition,
              headers,
            );

            const updatedCategories = await categoryRepository.find({
              where: { parent_id: parentCategory.id },
              order: { position: 'ASC' },
            });

            // Verify no positions changed
            updatedCategories.forEach((cat, idx) => {
              expect(cat.position).toBe(idx + 1);
              expect(cat.id).toBe(categories[idx].id);
            });
          });

          it('should handle moving to position zero', async () => {
            await expect(
              service.updateOrMoveCategory(
                categories[0].id,
                parentCategory.id,
                0,
                headers,
              ),
            ).rejects.toThrow('Please provide newPosition ');
            // verify positions are unchanged
            const updatedCategories = await categoryRepository.find({
              where: { parent_id: parentCategory.id },
              order: { position: 'ASC' },
            });

            updatedCategories.forEach((cat, idx) => {
              expect(cat.position).toBe(idx + 1);
            });
          });

          it('should handle moving to negative position', async () => {
            await expect(
              service.updateOrMoveCategory(
                categories[0].id,
                parentCategory.id,
                -1,
                headers,
              ),
            ).rejects.toThrow('Invalid new position');
            // verify positions are unchanged
            const updatedCategories = await categoryRepository.find({
              where: { parent_id: parentCategory.id },
              order: { position: 'ASC' },
            });

            updatedCategories.forEach((cat, idx) => {
              expect(cat.position).toBe(idx + 1);
            });
          });
        });
      });
    });

    describe('Name Validation Tests', () => {
      let existingCategory: CatalogCategory;

      beforeEach(async () => {
        // Create parent category
        const parentCategory = await categoryRepository.save({
          name: 'Parent Category',
          status: false,
          parent_id: 1,
          url_key: 'parent-category',
          position: 2,
        });

        // Create test category
        existingCategory = await categoryRepository.save({
          name: 'Test Category',
          status: false,
          parent_id: parentCategory.id,
          url_key: 'test-category',
          position: 1,
        });

        jest.clearAllMocks();
      });

      it('should successfully update category name', async () => {
        const updateDto: UpdateCatalogCategoryDto = {
          name: 'Updated Category Name',
        };

        const result = await service.updateCategory(
          updateDto,
          undefined,
          existingCategory.id,
          headers,
        );

        expect(result.categoryData.name).toBe('Updated Category Name');
      });

      it('should throw error for empty name', async () => {
        const updateDto: UpdateCatalogCategoryDto = plainToInstance(
          UpdateCatalogCategoryDto,
          {
            name: '',
          },
        );

        const errors = await validate(updateDto);

        // Assert validation errors
        expect(errors.length).toBeGreaterThan(0);
        expect(errors[0].constraints).toHaveProperty('isLength');
        expect(errors[0].constraints.isLength).toContain(
          'Category name must be between 2 and 50 characters.',
        );
      });

      it('should throw error for name that only contains spaces', async () => {
        const updateDto: UpdateCatalogCategoryDto = plainToInstance(
          UpdateCatalogCategoryDto,
          {
            name: '      ',
          },
        );

        const errors = await validate(updateDto);

        // Assert validation errors
        expect(errors.length).toBeGreaterThan(0);
        expect(errors[0].constraints).toHaveProperty('isLength');
        expect(errors[0].constraints.isLength).toContain(
          'Category name must be between 2 and 50 characters.',
        );
      });

      it('should throw error for duplicate name under same parent', async () => {
        // Create another category under same parent
        await categoryRepository.save({
          name: 'Existing Sibling',
          status: false,
          parent_id: existingCategory.parent_id,
          url_key: 'existing-sibling',
          position: 2,
        });

        const updateDto: UpdateCatalogCategoryDto = {
          name: 'Existing Sibling',
        };

        await expect(
          service.updateCategory(
            updateDto,
            undefined,
            existingCategory.id,
            headers,
          ),
        ).rejects.toThrow(
          'A category with the same name already exists for the same parent category',
        );
      });

      it('should handle name with special characters', async () => {
        const updateDto: UpdateCatalogCategoryDto = plainToInstance(
          UpdateCatalogCategoryDto,
          {
            name: 'Test & Category @ #$%',
          },
        );

        const result = await service.updateCategory(
          updateDto,
          undefined,
          existingCategory.id,
          headers,
        );
        expect(result.categoryData.name).toBe('Test & Category @ #$%');
      });

      it('should handle name with multiple spaces', async () => {
        const updateDto: UpdateCatalogCategoryDto = plainToInstance(
          UpdateCatalogCategoryDto,
          {
            name: '  Test   Category  Name  ',
          },
        );

        console.log({ updateDto });
        const result = await service.updateCategory(
          updateDto,
          undefined,
          existingCategory.id,
          headers,
        );
        expect(result.categoryData.name).toBe('Test Category Name');
      });

      it('should handle unicode characters in name', async () => {
        const updateDto: UpdateCatalogCategoryDto = plainToInstance(
          UpdateCatalogCategoryDto,
          {
            name: 'Test Category 测试类别',
          },
        );

        const result = await service.updateCategory(
          updateDto,
          undefined,
          existingCategory.id,
          headers,
        );
        expect(result.categoryData.name).toBe('Test Category 测试类别');
      });
    });

    describe('Field Validation Tests', () => {
      let existingCategory: CatalogCategory;

      beforeEach(async () => {
        existingCategory = await categoryRepository.save({
          name: 'Test Category',
          status: false,
          parent_id: 1,
          url_key: 'test-category',
          position: 2,
        });

        jest.clearAllMocks();
      });

      it('should validate meta title length', async () => {
        const updateDto: UpdateCatalogCategoryDto = {
          meta_title: 'a'.repeat(256),
        };

        const transformedInstance = plainToInstance(
          UpdateCatalogCategoryDto,
          updateDto,
        );

        const errors = await validate(transformedInstance);

        console.log({ mockCreateCategoryDto, errors });
        // Assert validation errors
        expect(errors.length).toBeGreaterThan(0);
        expect(errors[0].constraints).toHaveProperty('isLength');
        expect(errors[0].constraints.isLength).toContain(
          'meta_title must be between 2 and 50 characters.',
        );
      });

      it('should validate meta description length', async () => {
        const updateDto: UpdateCatalogCategoryDto = {
          meta_description: 'a'.repeat(1001),
        };
        const transformedInstance = plainToInstance(
          UpdateCatalogCategoryDto,
          updateDto,
        );

        const errors = await validate(transformedInstance);

        console.log({ mockCreateCategoryDto, errors });
        // Assert validation errors
        expect(errors.length).toBeGreaterThan(0);
        expect(errors[0].constraints).toHaveProperty('isLength');
        expect(errors[0].constraints.isLength).toContain(
          'meta_description must be between 2 and 150 characters.',
        );
      });

      it('should validate meta keywords length', async () => {
        const updateDto: UpdateCatalogCategoryDto = {
          meta_keyword: 'a'.repeat(256),
        };

        const transformedInstance = plainToInstance(
          UpdateCatalogCategoryDto,
          updateDto,
        );

        const errors = await validate(transformedInstance);

        // Assert validation errors
        expect(errors.length).toBeGreaterThan(0);
        expect(errors[0].constraints).toHaveProperty('isLength');
        expect(errors[0].constraints.isLength).toContain(
          'meta_keyword must be between 2 and 255 characters.',
        );
      });

      it('should properly convert string boolean values', async () => {
        const updateDto: UpdateCatalogCategoryDto = {
          status: 'true',
          include_in_menu: 'false',
        };

        const result = await service.updateCategory(
          updateDto,
          undefined,
          existingCategory.id,
          headers,
        );
        expect(result.categoryData.status).toBe(true);
        expect(result.categoryData.include_in_menu).toBe(false);
      });

      it('should handle invalid boolean string values', async () => {
        const updateDto: UpdateCatalogCategoryDto = plainToInstance(
          UpdateCatalogCategoryDto,
          {
            status: 'invalid-boolean',
          },
        );

        const errors = await validate(updateDto);

        // Assert validation errors
        expect(errors.length).toBeGreaterThan(0);
        expect(errors[0].constraints).toHaveProperty('isBooleanString');
        expect(errors[0].constraints.isBooleanString).toContain(
          'status must be a boolean string',
        );
      });

      it('should sanitize meta fields from XSS', async () => {
        const transformedInstance = plainToInstance(UpdateCatalogCategoryDto, {
          meta_title: '<script>alert("xss")</script>Meta Title',
          meta_description: '<script>alert("xss")</script>Meta Description',
          meta_keyword: '<script>alert("xss")</script>Keywords',
        });

        console.log({ transformedInstance });

        const result = await service.updateCategory(
          transformedInstance,
          undefined,
          existingCategory.id,
          headers,
        );
        expect(result.categoryData.meta_title).toBe('Meta Title');
        expect(result.categoryData.meta_description).toBe('Meta Description');
        expect(result.categoryData.meta_keyword).toBe('Keywords');
      });
    });

    describe('Media/Image Tests', () => {
      let existingCategory: CatalogCategory;
      const fileFilter = (req: any, file: any, callback: Function) => {
        if (file.mimetype.match(/\/(jpg|jpeg|png|gif|webp)$/)) {
          console.log('INVALID FILE TYPE');
          // callback(null, true);
        } else {
          callback(new BadRequestException('Invalid file type'), false);
        }
      };
      beforeEach(async () => {
        existingCategory = await categoryRepository.save({
          name: 'Test Category',
          status: false,
          parent_id: 1,
          url_key: 'test-category',
          position: 2,
        });

        jest.clearAllMocks();

        mockS3Service.uploadFilesToS3.mockResolvedValue(['test-image-url']);
        mockS3Service.deleteFiles.mockResolvedValue(undefined);
      });

      it('should successfully upload new image', async () => {
        const mockFile = {
          originalname: 'test-image.jpg',
          buffer: Buffer.from('test image content'),
        } as Express.Multer.File;

        const result = await service.updateCategory(
          {},
          mockFile,
          existingCategory.id,
          headers,
        );

        expect(mockS3Service.uploadFilesToS3).toHaveBeenCalled();
        expect(result.categoryData.image).toBe('test-image-url');
      });

      it('should replace existing image', async () => {
        // First update with initial image
        const initialFile = {
          originalname: 'initial-image.jpg',
          buffer: Buffer.from('initial image content'),
        } as Express.Multer.File;

        await service.updateCategory(
          {},
          initialFile,
          existingCategory.id,
          headers,
        );

        // Then update with new image
        const newFile = {
          originalname: 'new-image.jpg',
          buffer: Buffer.from('new image content'),
        } as Express.Multer.File;

        mockS3Service.uploadFilesToS3.mockResolvedValueOnce(['new-image-url']);

        const result = await service.updateCategory(
          {},
          newFile,
          existingCategory.id,
          headers,
        );
        expect(result.categoryData.image).toBe('new-image-url');
        expect(mockS3Service.deleteFiles).toHaveBeenCalled();
      });

      it('should handle image deletion', async () => {
        // First add an image
        const initialFile = {
          originalname: 'test-image.jpg',
          buffer: Buffer.from('test image content'),
        } as Express.Multer.File;

        await service.updateCategory(
          {},
          initialFile,
          existingCategory.id,
          headers,
        );

        // Then call delete endpoint
        await service.deleteCategoryImages(existingCategory.id);

        // Verify image was deleted
        const updatedCategory = await categoryRepository.findOne({
          where: { id: existingCategory.id },
        });
        expect(updatedCategory.image).toBeNull();
        expect(mockS3Service.deleteFiles).toHaveBeenCalled();
      });

      it('should validate image file type', async () => {
        const invalidFile = {
          originalname: 'test-doc.pdf',
          mimetype: 'pdf',
          buffer: Buffer.from('test doc content'),
        } as Express.Multer.File;

        await expect(
          service.updateCategory({}, invalidFile, existingCategory.id, headers),
        ).rejects.toThrow('Invalid file type');
      });

      it('should validate image file size', async () => {
        const largeFile = {
          originalname: 'large-image.jpg',
          buffer: Buffer.alloc(6 * 1024 * 1024), // 6MB
        } as Express.Multer.File;

        await expect(
          service.updateCategory({}, largeFile, existingCategory.id, headers),
        ).rejects.toThrow('File size exceeds limit');
      });

      it('should handle S3 upload failure', async () => {
        mockS3Service.uploadFilesToS3.mockRejectedValueOnce(
          new Error('Upload failed'),
        );

        const mockFile = {
          originalname: 'test-image.jpg',
          buffer: Buffer.from('test image content'),
        } as Express.Multer.File;

        await expect(
          service.updateCategory({}, mockFile, existingCategory.id, headers),
        ).rejects.toThrow();

        // Verify category wasn't updated
        const category = await categoryRepository.findOne({
          where: { id: existingCategory.id },
        });
        expect(category.image).toBeNull();
      });

      // it('should handle multiple files error', async () => {
      //   const files = [
      //     {
      //       originalname: 'image1.jpg',
      //       buffer: Buffer.from('content1'),
      //     },
      //     {
      //       originalname: 'image2.jpg',
      //       buffer: Buffer.from('content2'),
      //     },
      //   ] as Express.Multer.File[];

      //   await expect(
      //     service.updateCategory({}, files, existingCategory.id, headers),
      //   ).rejects.toThrow('Only one image file allowed');
      // });

      it('should maintain image URL format', async () => {
        const mockFile = {
          originalname: 'test-image.jpg',
          buffer: Buffer.from('test image content'),
        } as Express.Multer.File;

        mockS3Service.uploadFilesToS3.mockResolvedValueOnce([
          's3/category-images/test-image.jpg',
        ]);

        const result = await service.updateCategory(
          {},
          mockFile,
          existingCategory.id,
          headers,
        );

        expect(result.categoryData.image).toMatch(
          /^s3\/category-images\/.+\.jpg$/,
        );
      });
    });
  });

  describe('addProductsToCategory', () => {
    let testCategory: CatalogCategory;
    let existingProducts: CatalogProduct[];

    beforeEach(async () => {
      // Create test category
      testCategory = await categoryRepository.save({
        name: 'Test Category',
        status: false,
        parent_id: 1,
        url_key: 'test-category',
        position: 1,
      });

      // Create test products
      existingProducts = await Promise.all([
        catalogProductRepository.save({
          sku: 'test-product-1',
          status: true,
          type_id: 'simple',
        }),
        catalogProductRepository.save({
          sku: 'test-product-2',
          status: true,
          type_id: 'simple',
        }),
        catalogProductRepository.save({
          sku: 'test-product-3',
          status: true,
          type_id: 'simple',
        }),
      ]);

      jest.clearAllMocks();
    });

    it('should successfully add products to category with positions', async () => {
      const productsToAdd = [
        { id: existingProducts[0].id, position: 1 },
        { id: existingProducts[1].id, position: 2 },
      ];

      const result = await service.addProductsToCategory(
        testCategory.id,
        productsToAdd,
        headers,
      );

      // Verify products were added
      const categoryProducts = await productCategoryRelationRepository.find({
        where: { category: { id: testCategory.id } },
        relations: ['product'],
        order: { position: 'ASC' },
      });

      expect(categoryProducts).toHaveLength(2);
      expect(categoryProducts[0].product.id).toBe(existingProducts[0].id);
      expect(categoryProducts[0].position).toBe(1);
      expect(categoryProducts[1].product.id).toBe(existingProducts[1].id);
      expect(categoryProducts[1].position).toBe(2);

      // Verify return value
      expect(result.addedProducts).toHaveLength(2);
      expect(result.notFoundProducts).toHaveLength(0);

      // Verify MongoDB sync was called
      // TODO test this later on
      // expect(
      //   mockExternalApiHelper.sendCategoryInMongoDbEventBus,
      // ).toHaveBeenCalledWith(
      //   expect.objectContaining({
      //     entity_id: testCategory.id,
      //   }),
      // );
    });

    it('should not add products that are already in category', async () => {
      // First add some products
      await service.addProductsToCategory(
        testCategory.id,
        [{ id: existingProducts[0].id, position: 1 }],
        headers,
      );

      // Try to add same product again along with new one
      const result = await service.addProductsToCategory(
        testCategory.id,
        [
          { id: existingProducts[0].id, position: 2 },
          { id: existingProducts[1].id, position: 3 },
        ],
        headers,
      );

      // Only the new product should be added
      const categoryProducts = await productCategoryRelationRepository.find({
        where: { category: { id: testCategory.id } },
        relations: ['product'],
      });

      expect(categoryProducts).toHaveLength(2);
      expect(result.addedProducts).toHaveLength(1);
      expect(result.addedProducts[0].id).toBe(existingProducts[1].id);
    });

    it('should handle non-existent products', async () => {
      const result = await service.addProductsToCategory(
        testCategory.id,
        [
          { id: 99999, position: 1 },
          { id: existingProducts[0].id, position: 2 },
        ],
        headers,
      );

      expect(result.addedProducts).toHaveLength(1);
      expect(result.notFoundProducts).toHaveLength(1);
      expect(result.notFoundProducts[0].id).toBe(99999);
    });

    it('should throw error for invalid category ID', async () => {
      try {
        await service.addProductsToCategory(
          99999,
          [{ id: existingProducts[0].id, position: 1 }],
          headers,
        );
        fail('Should have thrown an error');
      } catch (error) {
        expect(error.status).toBe(400);
        expect(error.message).toBe('Category does not exist');
      }
    });

    it('should handle empty product list', async () => {
      const result = await service.addProductsToCategory(
        testCategory.id,
        [],
        headers,
      );
      expect(result.addedProducts).toHaveLength(0);
      expect(result.notFoundProducts).toHaveLength(0);
    });

    // it('should maintain transaction integrity on error', async () => {
    //   // Mock MongoDB sync to fail
    //   mockExternalApiHelper.sendCategoryInMongoDbEventBus.mockRejectedValueOnce(
    //     new Error('Sync failed'),
    //   );

    //   const productsToAdd = [
    //     { id: existingProducts[0].id, position: 1 },
    //     { id: existingProducts[1].id, position: 2 },
    //   ];

    //   await expect(
    //     service.addProductsToCategory(testCategory.id, productsToAdd, headers),
    //   ).rejects.toThrow();

    //   // Verify no products were added
    //   const categoryProducts = await productCategoryRelationRepository.find({
    //     where: { category: { id: testCategory.id } },
    //   });

    //   expect(categoryProducts).toHaveLength(0);
    // });

    it('should handle concurrent product additions', async () => {
      const addPromises = [
        service.addProductsToCategory(
          testCategory.id,
          [{ id: existingProducts[0].id, position: 1 }],
          headers,
        ),
        service.addProductsToCategory(
          testCategory.id,
          [{ id: existingProducts[0].id, position: 2 }],
          headers,
        ),
      ];

      await Promise.all(addPromises);

      // Verify product was only added once
      const categoryProducts = await productCategoryRelationRepository.find({
        where: { category: { id: testCategory.id } },
        relations: {
          product: true,
          category: true,
        },
      });
      expect(categoryProducts).toHaveLength(1);
    });
  });

  describe('removeProductsFromCategory', () => {
    let testCategory: CatalogCategory;
    let testProducts: CatalogProduct[];

    beforeEach(async () => {
      // Create test category
      testCategory = await categoryRepository.save({
        name: 'Test Category',
        status: false,
        parent_id: 1,
        url_key: 'test-category',
        position: 1,
      });

      // Create test products
      testProducts = await Promise.all([
        catalogProductRepository.save({
          sku: 'test-product-1',
          status: true,
          type_id: 'simple',
        }),
        catalogProductRepository.save({
          sku: 'test-product-2',
          status: true,
          type_id: 'simple',
        }),
        catalogProductRepository.save({
          sku: 'test-product-3',
          status: true,
          type_id: 'simple',
        }),
      ]);

      // Add products to category
      await Promise.all(
        testProducts.map((product, index) =>
          productCategoryRelationRepository.save({
            category: testCategory,
            product: product,
            position: index + 1,
          }),
        ),
      );

      jest.clearAllMocks();
    });

    it('should successfully remove products from category', async () => {
      const productsToRemove = [testProducts[0].id, testProducts[1].id];

      const result = await service.removeProductsFromCategoryV2(
        testCategory.id,
        productsToRemove,
        headers,
      );

      // Verify products were removed
      const remainingProducts = await productCategoryRelationRepository.find({
        where: { category: { id: testCategory.id } },
        relations: ['product'],
      });

      expect(remainingProducts).toHaveLength(1);
      expect(remainingProducts[0].product.id).toBe(testProducts[2].id);

      // Verify return value
      expect(result.removedProducts).toHaveLength(2);
      expect(result.removedProducts).toContain(testProducts[0].id);
      expect(result.removedProducts).toContain(testProducts[1].id);
      expect(result.notFoundProducts).toHaveLength(0);

      // Verify MongoDB sync was called
      // TODO will be tested later on
      // expect(
      //   mockExternalApiHelper.sendCategoryInMongoDbEventBus,
      // ).toHaveBeenCalledWith(
      //   expect.objectContaining({
      //     entity_id: testCategory.id,
      //   }),
      // );
    });

    it('should handle non-existent products gracefully', async () => {
      const nonExistentId = 99999;
      const productsToRemove = [nonExistentId, testProducts[0].id];

      const result = await service.removeProductsFromCategoryV2(
        testCategory.id,
        productsToRemove,
        headers,
      );

      expect(result.removedProducts).toHaveLength(1);
      expect(result.removedProducts[0]).toBe(testProducts[0].id);
      expect(result.notFoundProducts).toHaveLength(1);
      expect(result.notFoundProducts[0]).toBe(nonExistentId);

      // Verify only existing product was removed
      const remainingProducts = await productCategoryRelationRepository.find({
        where: { category: { id: testCategory.id } },
      });
      expect(remainingProducts).toHaveLength(2);
    });

    it('should handle empty product list', async () => {
      const result = await service.removeProductsFromCategoryV2(
        testCategory.id,
        [],
        headers,
      );

      expect(result.removedProducts).toHaveLength(0);
      expect(result.notFoundProducts).toHaveLength(0);

      // Verify no products were removed
      const remainingProducts = await productCategoryRelationRepository.find({
        where: { category: { id: testCategory.id } },
      });
      expect(remainingProducts).toHaveLength(3);
    });

    it('should handle all non-existent products', async () => {
      const result = await service.removeProductsFromCategoryV2(
        testCategory.id,
        [99999, 88888],
        headers,
      );

      expect(result.removedProducts).toHaveLength(0);
      expect(result.notFoundProducts).toHaveLength(2);

      // Verify no products were removed
      const remainingProducts = await productCategoryRelationRepository.find({
        where: { category: { id: testCategory.id } },
      });
      expect(remainingProducts).toHaveLength(3);
    });

    // it('should maintain transaction integrity on error', async () => {
    //   // Mock MongoDB sync to fail
    //   mockExternalApiHelper.sendCategoryInMongoDbEventBus.mockRejectedValueOnce(
    //     new Error('Sync failed'),
    //   );

    //   await expect(
    //     service.removeProductsFromCategoryV2(
    //       testCategory.id,
    //       [testProducts[0].id],
    //       headers,
    //     ),
    //   ).rejects.toThrow();

    //   // Verify no products were removed
    //   const remainingProducts = await productCategoryRelationRepository.find({
    //     where: { category: { id: testCategory.id } },
    //   });
    //   expect(remainingProducts).toHaveLength(3);
    // });

    it('should handle concurrent removal operations', async () => {
      const removePromises = [
        service.removeProductsFromCategoryV2(
          testCategory.id,
          [testProducts[0].id],
          headers,
        ),
        service.removeProductsFromCategoryV2(
          testCategory.id,
          [testProducts[0].id],
          headers,
        ),
      ];

      await Promise.all(removePromises);

      // Verify product was removed only once
      const remainingProducts = await productCategoryRelationRepository.find({
        where: { category: { id: testCategory.id } },
      });
      expect(remainingProducts).toHaveLength(2);
    });

    it('should handle removing all products from category', async () => {
      const allProductIds = testProducts.map((product) => product.id);

      const result = await service.removeProductsFromCategoryV2(
        testCategory.id,
        allProductIds,
        headers,
      );

      expect(result.removedProducts).toHaveLength(3);
      expect(result.notFoundProducts).toHaveLength(0);

      // Verify all products were removed
      const remainingProducts = await productCategoryRelationRepository.find({
        where: { category: { id: testCategory.id } },
      });
      expect(remainingProducts).toHaveLength(0);
    });

    it('should preserve other category associations when removing products', async () => {
      // Create another category and add same products
      const anotherCategory = await categoryRepository.save({
        name: 'Another Category',
        status: false,
        parent_id: 1,
        url_key: 'another-category',
        position: 2,
      });

      await Promise.all(
        testProducts.map((product) =>
          productCategoryRelationRepository.save({
            category: anotherCategory,
            product: product,
            position: 1,
          }),
        ),
      );

      // Remove products from test category
      await service.removeProductsFromCategoryV2(
        testCategory.id,
        testProducts.map((p) => p.id),
        headers,
      );

      // Verify products still exist in other category
      const otherCategoryProducts =
        await productCategoryRelationRepository.find({
          where: { category: { id: anotherCategory.id } },
        });
      expect(otherCategoryProducts).toHaveLength(3);
    });

    it('should handle removing products by invalid category ID', async () => {
      await expect(
        service.removeProductsFromCategoryV2(
          99999,
          [testProducts[0].id],
          headers,
        ),
      ).rejects.toThrow();

      // Verify no products were removed
      const remainingProducts = await productCategoryRelationRepository.find({
        where: { category: { id: testCategory.id } },
      });
      expect(remainingProducts).toHaveLength(3);
    });

    it('should clean up orphaned category-product relations', async () => {
      // Delete a product directly
      await catalogProductRepository.remove(testProducts[0]);

      // Try to remove the deleted product from category
      const result = await service.removeProductsFromCategoryV2(
        testCategory.id,
        [testProducts[0].id],
        headers,
      );

      expect(result.removedProducts).toHaveLength(0);
      expect(result.notFoundProducts).toHaveLength(1);

      // Verify relations are cleaned up
      const relations = await productCategoryRelationRepository.find({
        where: { category: { id: testCategory.id } },
      });
      expect(relations).toHaveLength(2);
    });
  });

  describe('deleteCatalogCategory', () => {
    let testCategory: CatalogCategory;
    let testProducts: CatalogProduct[];

    beforeEach(async () => {
      // Create test category
      testCategory = await categoryRepository.save({
        name: 'Test Category',
        status: false,
        parent_id: 1,
        url_key: 'test-category',
        position: 1,
      });

      // Create test products
      testProducts = await Promise.all([
        catalogProductRepository.save({
          sku: 'test-product-1',
          status: true,
          type_id: 'simple',
        }),
        catalogProductRepository.save({
          sku: 'test-product-2',
          status: true,
          type_id: 'simple',
        }),
        catalogProductRepository.save({
          sku: 'test-product-3',
          status: true,
          type_id: 'simple',
        }),
      ]);

      // Add products to category
      await service.addProductsToCategory(
        testCategory.id,
        [
          { id: testProducts[0].id, position: 1 },
          { id: testProducts[1].id, position: 2 },
          { id: testProducts[2].id, position: 3 },
        ],
        headers,
      );

      jest.clearAllMocks();
    });

    it('should successfully delete category and its product relations', async () => {
      const result = await service.deleteCatalogCategory(testCategory.id);

      // Verify category was deleted
      const deletedCategory = await categoryRepository.findOne({
        where: { id: testCategory.id },
      });
      expect(deletedCategory).toBeNull();

      // Verify product relations were deleted
      const categoryRelations = await productCategoryRelationRepository.find({
        where: { category: { id: testCategory.id } },
      });
      expect(categoryRelations).toHaveLength(0);

      // Verify products still exist
      const products = await catalogProductRepository.find({
        where: { id: In(testProducts.map((p) => p.id)) },
      });
      expect(products).toHaveLength(3);

      // Verify response format
      expect(result).toEqual({
        id: testCategory.id,
        response:
          'Catalog category and associated product-category relations deleted successfully',
      });
    });

    it('should throw error when deleting non-existent category', async () => {
      try {
        await service.deleteCatalogCategory(99999);
        fail('Should have thrown an error');
      } catch (error) {
        expect(error.status).toBe(400);
        expect(error.message).toBe('Category not found');
      }
    });

    it('should handle deleting category with no product relations', async () => {
      // Create category without products
      const emptyCategory = await categoryRepository.save({
        name: 'Empty Category',
        status: false,
        parent_id: 1,
        url_key: 'empty-category',
        position: 2,
      });

      const result = await service.deleteCatalogCategory(emptyCategory.id);

      expect(result.id).toBe(emptyCategory.id);
      const deletedCategory = await categoryRepository.findOne({
        where: { id: emptyCategory.id },
      });
      expect(deletedCategory).toBeNull();
    });

    it('should preserve products when deleting their category', async () => {
      await service.deleteCatalogCategory(testCategory.id);

      // Verify products still exist and are unchanged
      const remainingProducts = await catalogProductRepository.find({
        where: { id: In(testProducts.map((p) => p.id)) },
      });
      expect(remainingProducts).toHaveLength(3);
      remainingProducts.forEach((product) => {
        const originalProduct = testProducts.find((p) => p.id === product.id);
        expect(product).toMatchObject({
          sku: originalProduct.sku,
          status: originalProduct.status,
          type_id: originalProduct.type_id,
        });
      });
    });

    it('should handle concurrent category deletions', async () => {
      const deletePromises = [
        service.deleteCatalogCategory(testCategory.id),
        service.deleteCatalogCategory(testCategory.id),
      ];

      await expect(Promise.all(deletePromises)).rejects.toThrow();

      // Verify category was deleted only once
      const deletedCategory = await categoryRepository.findOne({
        where: { id: testCategory.id },
      });
      expect(deletedCategory).toBeNull();
    });

    it('should handle deleting category with multiple product associations', async () => {
      // Add same products to another category
      const anotherCategory = await categoryRepository.save({
        name: 'Another Category',
        status: false,
        parent_id: 1,
        url_key: 'another-category',
        position: 2,
      });

      await Promise.all(
        testProducts.map((product) =>
          productCategoryRelationRepository.save({
            category: anotherCategory,
            product: product,
            position: 1,
          }),
        ),
      );

      await service.deleteCatalogCategory(testCategory.id);

      // Verify products still exist in other category
      const otherCategoryProducts =
        await productCategoryRelationRepository.find({
          where: { category: { id: anotherCategory.id } },
          relations: ['product'],
        });
      expect(otherCategoryProducts).toHaveLength(3);
    });

    it('should handle deleting root category', async () => {
      // Try to delete root category (id: 1)
      const rootCategory = await categoryRepository.findOne({
        where: { id: 1 },
      });

      if (rootCategory) {
        await expect(service.deleteCatalogCategory(1)).rejects.toThrow();
      }
    });

    it('should maintain database integrity during failed deletion', async () => {
      // Mock repository to fail during deletion
      jest
        .spyOn(categoryRepository, 'delete')
        .mockRejectedValueOnce(new Error('Database error'));

      await expect(
        service.deleteCatalogCategory(testCategory.id),
      ).rejects.toThrow();

      // Verify category and relations still exist
      const category = await categoryRepository.findOne({
        where: { id: testCategory.id },
      });
      expect(category).not.toBeNull();

      const relations = await productCategoryRelationRepository.find({
        where: { category: { id: testCategory.id } },
      });
      expect(relations).toHaveLength(3);
    });

    it('should handle deletion of category with child categories', async () => {
      // Create child category
      const childCategory = await categoryRepository.save({
        name: 'Child Category',
        status: false,
        parent_id: testCategory.id,
        url_key: 'child-category',
        position: 1,
      });

      // Add products to child category
      await productCategoryRelationRepository.save({
        category: childCategory,
        product: testProducts[0],
        position: 1,
      });

      await service.deleteCatalogCategory(testCategory.id);

      // Verify both categories are deleted
      const deletedParent = await categoryRepository.findOne({
        where: { id: testCategory.id },
      });
      const deletedChild = await categoryRepository.findOne({
        where: { id: childCategory.id },
      });

      expect(deletedParent).toBeNull();
      expect(deletedChild).toBeNull(); // TODO: BL - check if child category should be deleted
    });
  });

  describe('deleteCategoryImages', () => {
    let testCategory: CatalogCategory;

    beforeEach(async () => {
      // Create test category with image
      testCategory = await categoryRepository.save({
        name: 'Test Category',
        status: false,
        parent_id: 1,
        url_key: 'test-category',
        position: 1,
        image: 'category-images/test-image.jpg',
      });

      jest.clearAllMocks();
    });

    it('should successfully delete category image', async () => {
      const result = await service.deleteCategoryImages(testCategory.id);

      // Verify S3 deletion was called with correct key
      expect(mockS3Service.deleteFiles).toHaveBeenCalledWith([
        'category-images/test-image.jpg',
      ]);

      // Verify image field was cleared in database
      const updatedCategory = await categoryRepository.findOne({
        where: { id: testCategory.id },
      });
      expect(updatedCategory.image).toBeNull();

      // Verify response
      expect(result).toEqual({
        message: 'S3 category image deleted successfully',
      });
    });

    it('should throw error for invalid category ID', async () => {
      try {
        await expect(service.deleteCategoryImages(99999));
      } catch (error) {
        expect(error.status).toBe(400);
        expect(error.message).toBe('Invalid category Id');
      }

      // Verify S3 deletion was not called
      expect(mockS3Service.deleteFiles).not.toHaveBeenCalled();
    });

    it('should handle category with no image', async () => {
      // Create category without image
      const categoryWithoutImage = await categoryRepository.save({
        name: 'No Image Category',
        status: false,
        parent_id: 1,
        url_key: 'no-image-category',
        position: 2,
        image: null,
      });

      const result = await service.deleteCategoryImages(
        categoryWithoutImage.id,
      );

      // Verify S3 deletion was called with empty key
      expect(mockS3Service.deleteFiles).toHaveBeenCalledWith(['']);

      expect(result).toEqual({
        message: 'S3 category image deleted successfully',
      });
    });

    it('should handle S3 deletion failure', async () => {
      // Mock S3 service to fail
      mockS3Service.deleteFiles.mockRejectedValueOnce(
        new Error('S3 deletion failed'),
      );

      await expect(
        service.deleteCategoryImages(testCategory.id),
      ).rejects.toThrow('S3 deletion failed');

      // Verify image field was not cleared
      const category = await categoryRepository.findOne({
        where: { id: testCategory.id },
      });
      expect(category.image).toBe('category-images/test-image.jpg');
    });

    it('should handle concurrent image deletions', async () => {
      const deletePromises = [
        service.deleteCategoryImages(testCategory.id),
        service.deleteCategoryImages(testCategory.id),
      ];

      await Promise.all(deletePromises);

      // Verify S3 deletion was called only once
      expect(mockS3Service.deleteFiles).toHaveBeenCalledTimes(1);

      // Verify image field was cleared
      const category = await categoryRepository.findOne({
        where: { id: testCategory.id },
      });
      expect(category.image).toBeNull();
    });

    it('should correctly extract image key from full URL', async () => {
      // Create category with full S3 URL
      const categoryWithFullUrl = await categoryRepository.save({
        name: 'Full URL Category',
        status: false,
        parent_id: 1,
        url_key: 'full-url-category',
        position: 3,
        image:
          'https://s3.amazonaws.com/bucket/category-images/full-url-test.jpg',
      });

      await service.deleteCategoryImages(categoryWithFullUrl.id);

      // Verify correct key extraction
      expect(mockS3Service.deleteFiles).toHaveBeenCalledWith([
        'category-images/full-url-test.jpg',
      ]);
    });

    it('should handle special characters in image filename', async () => {
      const categoryWithSpecialChars = await categoryRepository.save({
        name: 'Special Chars Category',
        status: false,
        parent_id: 1,
        url_key: 'special-chars-category',
        position: 4,
        image: 'category-images/test image@#$%.jpg',
      });

      await service.deleteCategoryImages(categoryWithSpecialChars.id);

      // Verify correct handling of special characters
      expect(mockS3Service.deleteFiles).toHaveBeenCalledWith([
        'category-images/test image@#$%.jpg',
      ]);
    });

    it('should maintain transaction integrity', async () => {
      // Mock repository update to fail
      jest
        .spyOn(categoryRepository, 'update')
        .mockRejectedValueOnce(new Error('Update failed'));

      await expect(
        service.deleteCategoryImages(testCategory.id),
      ).rejects.toThrow();

      // Verify S3 deletion was not called
      expect(mockS3Service.deleteFiles).not.toHaveBeenCalled();

      // Verify image field remains unchanged
      const category = await categoryRepository.findOne({
        where: { id: testCategory.id },
      });
      expect(category.image).toBe('category-images/test-image.jpg');
    });

    it('should handle empty string image field', async () => {
      const categoryWithEmptyImage = await categoryRepository.save({
        name: 'Empty Image Category',
        status: false,
        parent_id: 1,
        url_key: 'empty-image-category',
        position: 5,
        image: '',
      });

      const result = await service.deleteCategoryImages(
        categoryWithEmptyImage.id,
      );

      // Verify S3 deletion was called with empty string
      expect(mockS3Service.deleteFiles).toHaveBeenCalledWith(['']);

      expect(result).toEqual({
        message: 'S3 category image deleted successfully',
      });
    });

    it('should handle undefined image field', async () => {
      const categoryWithUndefinedImage = await categoryRepository.save({
        name: 'Undefined Image Category',
        status: false,
        parent_id: 1,
        url_key: 'undefined-image-category',
        position: 6,
        image: undefined,
      });

      await expect(
        service.deleteCategoryImages(categoryWithUndefinedImage.id),
      ).rejects.toThrow();
    });
  });

  describe('getCategoryTree', () => {
    let topLevelCategories: CatalogCategory[];
    let subCategories: CatalogCategory[];
    let subSubCategories: CatalogCategory[];

    beforeEach(async () => {
      // Create root category (id: 2 as per implementation)
      await categoryRepository.save({
        id: 2,
        name: 'Root Category',
        status: true,
        parent_id: 1,
        url_key: 'root-category',
        position: 1,
      });

      // Create top-level categories
      topLevelCategories = await Promise.all([
        categoryRepository.save({
          name: 'Category 1',
          status: true,
          parent_id: 2,
          url_key: 'category-1',
          position: 1,
        }),
        categoryRepository.save({
          name: 'Category 2',
          status: true,
          parent_id: 2,
          url_key: 'category-2',
          position: 2,
        }),
      ]);

      // Create sub-categories under Category 1
      subCategories = await Promise.all([
        categoryRepository.save({
          name: 'Sub Category 1',
          status: true,
          parent_id: topLevelCategories[0].id,
          url_key: 'sub-category-1',
          position: 1,
        }),
        categoryRepository.save({
          name: 'Sub Category 2',
          status: true,
          parent_id: topLevelCategories[0].id,
          url_key: 'sub-category-2',
          position: 2,
        }),
      ]);

      // Create sub-sub-categories under Sub Category 1
      subSubCategories = await Promise.all([
        categoryRepository.save({
          name: 'Sub Sub Category 1',
          status: true,
          parent_id: subCategories[0].id,
          url_key: 'sub-sub-category-1',
          position: 1,
        }),
        categoryRepository.save({
          name: 'Sub Sub Category 2',
          status: true,
          parent_id: subCategories[0].id,
          url_key: 'sub-sub-category-2',
          position: 2,
        }),
      ]);

      jest.clearAllMocks();
    });

    it('should return correctly structured category tree', async () => {
      const result = await service.getCategoryTree();

      expect(result).toHaveProperty('data.categoryList');
      expect(result.data.categoryList).toHaveLength(2);

      // Check structure of first top-level category
      const firstCategory = result.data.categoryList[0];
      expect(firstCategory).toMatchObject({
        name: 'Category 1',
        id: topLevelCategories[0].id,
        url_path: 'category-1',
        position: 1,
        path: 'Category 1',
        children: expect.any(Array),
      });

      // Check sub-categories
      expect(firstCategory.children).toHaveLength(2);
      expect(firstCategory.children[0]).toMatchObject({
        name: 'Sub Category 1',
        id: subCategories[0].id,
        url_path: 'sub-category-1',
        position: 1,
        path: 'Category 1/Sub Category 1',
        children: expect.any(Array),
      });

      // Check sub-sub-categories
      expect(firstCategory.children[0].children).toHaveLength(2);
      expect(firstCategory.children[0].children[0]).toMatchObject({
        name: 'Sub Sub Category 1',
        id: subSubCategories[0].id,
        url_path: 'sub-sub-category-1',
        position: 1,
        path: 'Category 1/Sub Category 1/Sub Sub Category 1',
        children: [],
      });
    });

    it('should respect category positions in tree structure', async () => {
      // Update positions in reverse order
      await categoryRepository.update(topLevelCategories[0].id, {
        position: 2,
      });
      await categoryRepository.update(topLevelCategories[1].id, {
        position: 1,
      });

      const result = await service.getCategoryTree();

      const categoryList = result.data.categoryList;
      expect(categoryList[0].name).toBe('Category 2');
      expect(categoryList[1].name).toBe('Category 1');
    });

    it('should handle empty category tree', async () => {
      // Delete all categories except root
      await categoryRepository.delete({ parent_id: Not(1) });

      const result = await service.getCategoryTree();

      expect(result.data.categoryList).toHaveLength(0);
    });

    it('should handle categories with no children', async () => {
      // Delete all sub-categories
      await categoryRepository.delete({
        id: In([
          ...subCategories.map((c) => c.id),
          ...subSubCategories.map((c) => c.id),
        ]),
      });

      const result = await service.getCategoryTree();

      expect(result.data.categoryList).toHaveLength(2);
      expect(result.data.categoryList[0].children).toHaveLength(0);
      expect(result.data.categoryList[1].children).toHaveLength(0);
    });

    it('should handle deep nested categories', async () => {
      // Create additional levels of nesting
      const deepNestedCategory = await categoryRepository.save({
        name: 'Deep Nested Category',
        status: true,
        parent_id: subSubCategories[0].id,
        url_key: 'deep-nested-category',
        position: 1,
      });

      const result = await service.getCategoryTree();

      // Navigate to the deepest category
      const deepestCategory =
        result.data.categoryList[0].children[0].children[0].children[0];

      expect(deepestCategory).toMatchObject({
        name: 'Deep Nested Category',
        id: deepNestedCategory.id,
        url_path: 'deep-nested-category',
        path: 'Category 1/Sub Category 1/Sub Sub Category 1/Deep Nested Category',
      });
    });

    it('should include correct paths for all categories', async () => {
      const result = await service.getCategoryTree();

      // Check paths at each level
      const topLevel = result.data.categoryList[0];
      expect(topLevel.path).toBe('Category 1');

      const subLevel = topLevel.children[0];
      expect(subLevel.path).toBe('Category 1/Sub Category 1');

      const subSubLevel = subLevel.children[0];
      expect(subSubLevel.path).toBe(
        'Category 1/Sub Category 1/Sub Sub Category 1',
      );
    });

    it('should maintain consistent order at all levels', async () => {
      // Update positions at each level
      await categoryRepository.update(subCategories[0].id, { position: 2 });
      await categoryRepository.update(subCategories[1].id, { position: 1 });
      await categoryRepository.update(subSubCategories[0].id, { position: 2 });
      await categoryRepository.update(subSubCategories[1].id, { position: 1 });

      const result = await service.getCategoryTree();

      const firstTopLevel = result.data.categoryList[0];
      expect(firstTopLevel.children[0].name).toBe('Sub Category 2');
      expect(firstTopLevel.children[1].name).toBe('Sub Category 1');
      expect(firstTopLevel.children[1].children[0].name).toBe(
        'Sub Sub Category 2',
      );
      expect(firstTopLevel.children[1].children[1].name).toBe(
        'Sub Sub Category 1',
      );
    });

    it('should handle special characters in category names', async () => {
      const specialCharCategory = await categoryRepository.save({
        name: 'Special & Chars @ Category!',
        status: true,
        parent_id: 2,
        url_key: 'special-chars-category',
        position: 3,
      });

      const result = await service.getCategoryTree();

      const specialCategory = result.data.categoryList.find(
        (c) => c.id === specialCharCategory.id,
      );
      expect(specialCategory.path).toBe('Special & Chars @ Category!');
    });

    it('should handle concurrent tree updates', async () => {
      // Simulate concurrent category creations
      const createPromises = [
        categoryRepository.save({
          name: 'Concurrent Category 1',
          status: true,
          parent_id: 2,
          url_key: 'concurrent-1',
          position: 3,
        }),
        categoryRepository.save({
          name: 'Concurrent Category 2',
          status: true,
          parent_id: 2,
          url_key: 'concurrent-2',
          position: 4,
        }),
      ];

      await Promise.all(createPromises);

      const result = await service.getCategoryTree();
      expect(result.data.categoryList).toHaveLength(4);
    });

    it('should throw error on database failure', async () => {
      // Mock repository to fail
      jest
        .spyOn(categoryRepository, 'find')
        .mockRejectedValueOnce(new Error('Database error'));

      await expect(service.getCategoryTree()).rejects.toThrow(
        'Failed to fetch category tree',
      );
    });

    it('should return empty children array for leaf categories', async () => {
      const result = await service.getCategoryTree();

      // Check all leaf nodes have empty children arrays
      const leafCategory = result.data.categoryList[0].children[0].children[0];
      expect(leafCategory.children).toEqual([]);
    });
  });

  describe('updateProductsPositionInCategory', () => {
    let testCategory: CatalogCategory;
    let testProducts: CatalogProduct[];

    beforeEach(async () => {
      // Create test category
      testCategory = await categoryRepository.save({
        name: 'Test Category',
        status: false,
        parent_id: 1,
        url_key: 'test-category',
        position: 1,
      });

      // Create test products
      testProducts = await Promise.all([
        catalogProductRepository.save({
          sku: 'test-product-1',
          status: true,
          type_id: 'simple',
        }),
        catalogProductRepository.save({
          sku: 'test-product-2',
          status: true,
          type_id: 'simple',
        }),
        catalogProductRepository.save({
          sku: 'test-product-3',
          status: true,
          type_id: 'simple',
        }),
      ]);

      // Add products to category with initial positions
      await service.addProductsToCategory(
        testCategory.id,
        [
          { id: testProducts[0].id, position: 1 },
          { id: testProducts[1].id, position: 2 },
          { id: testProducts[2].id, position: 3 },
        ],
        headers,
      );
      jest.clearAllMocks();
    });

    it('should successfully update product positions', async () => {
      const updateData = {
        category_id: testCategory.id,
        product_positions_data: [
          { id: testProducts[0].id, position: 3 },
          { id: testProducts[1].id, position: 1 },
          { id: testProducts[2].id, position: 2 },
        ],
      };

      const result = await service.updateProductsPositionInCategoryV2(
        updateData,
        headers,
      );

      // Verify positions were updated
      const updatedRelations = await productCategoryRelationRepository.find({
        where: { category: { id: testCategory.id } },
        relations: ['product'],
        order: { position: 'ASC' },
      });

      expect(updatedRelations[0].product.id).toBe(testProducts[1].id);
      expect(updatedRelations[0].position).toBe(1);
      expect(updatedRelations[1].product.id).toBe(testProducts[2].id);
      expect(updatedRelations[1].position).toBe(2);
      expect(updatedRelations[2].product.id).toBe(testProducts[0].id);
      expect(updatedRelations[2].position).toBe(3);

      // Verify MongoDB sync was called
      // expect(
      //   mockExternalApiHelper.sendCategoryInMongoDbEventBus,
      // ).toHaveBeenCalledWith(
      //   expect.objectContaining({
      //     entity_id: testCategory.id,
      //   }),
      // );

      // Verify response
      expect(result).toEqual({ response: 'Positions updated successfully' });
    });

    it('should throw error when category_id is missing', async () => {
      const updateData = {
        product_positions_data: [{ id: testProducts[0].id, position: 2 }],
      };

      try {
        await service.updateProductsPositionInCategoryV2(updateData, headers);
        fail('Should have thrown an error');
      } catch (error) {
        expect(error.status).toBe(400);
        expect(error.message).toBe(
          'Please add both category id and product positions data in the request',
        );
      }
    });

    it('should throw error when product_positions_data is empty', async () => {
      const updateData = {
        category_id: testCategory.id,
        product_positions_data: [],
      };

      try {
        await service.updateProductsPositionInCategoryV2(updateData, headers);
        fail('Should have thrown an error');
      } catch (error) {
        expect(error.status).toBe(400);
        expect(error.message).toBe(
          'Please add both category id and product positions data in the request',
        );
      }
    });

    it('should throw error for invalid category ID', async () => {
      const updateData = {
        category_id: 99999,
        product_positions_data: [{ id: testProducts[0].id, position: 1 }],
      };

      try {
        await service.updateProductsPositionInCategoryV2(updateData, headers);
        fail('Should have thrown an error');
      } catch (error) {
        expect(error.status).toBe(400);
        expect(error.message).toBe('Category not found');
      }
    });

    it('should handle non-existent products gracefully', async () => {
      const updateData = {
        category_id: testCategory.id,
        product_positions_data: [
          { id: 99999, position: 1 },
          { id: testProducts[0].id, position: 2 },
        ],
      };

      await service.updateProductsPositionInCategoryV2(updateData, headers);

      // Verify only existing product was updated
      const updatedRelations = await productCategoryRelationRepository.find({
        where: { category: { id: testCategory.id } },
        relations: ['product'],
      });

      const updatedProduct = updatedRelations.find(
        (r) => r.product.id === testProducts[0].id,
      );
      expect(updatedProduct.position).toBe(2);
    });

    it('should handle concurrent position updates', async () => {
      const updateData = {
        category_id: testCategory.id,
        product_positions_data: [{ id: testProducts[0].id, position: 2 }],
      };

      const updatePromises = [
        service.updateProductsPositionInCategoryV2(updateData, headers),
        service.updateProductsPositionInCategoryV2(updateData, headers),
      ];

      await Promise.all(updatePromises);

      // Verify final position is correct
      const finalRelation = await productCategoryRelationRepository.findOne({
        where: {
          category: { id: testCategory.id },
          product: { id: testProducts[0].id },
        },
      });
      expect(finalRelation.position).toBe(2);
    });

    it('should maintain data integrity on MongoDB sync failure', async () => {
      // Mock MongoDB sync to fail
      mockExternalApiHelper.sendCategoryInMongoDbEventBus.mockRejectedValueOnce(
        new Error('MongoDB sync failed'),
      );

      const updateData = {
        category_id: testCategory.id,
        product_positions_data: [{ id: testProducts[0].id, position: 2 }],
      };

      await expect(
        service.updateProductsPositionInCategoryV2(updateData, headers),
      ).rejects.toThrow();

      // Verify positions remain unchanged
      const relations = await productCategoryRelationRepository.find({
        where: { category: { id: testCategory.id } },
        relations: ['product'],
        order: { position: 'ASC' },
      });

      expect(relations[0].product.id).toBe(testProducts[0].id);
      expect(relations[0].position).toBe(1);
    });

    it('should handle multiple position updates in single request', async () => {
      const updateData = {
        category_id: testCategory.id,
        product_positions_data: testProducts.map((product, index) => ({
          id: product.id,
          position: testProducts.length - index, // Reverse positions
        })),
      };

      await service.updateProductsPositionInCategoryV2(updateData, headers);

      const updatedRelations = await productCategoryRelationRepository.find({
        where: { category: { id: testCategory.id } },
        relations: ['product'],
        order: { position: 'ASC' },
      });

      expect(updatedRelations[0].product.id).toBe(testProducts[2].id);
      expect(updatedRelations[1].product.id).toBe(testProducts[1].id);
      expect(updatedRelations[2].product.id).toBe(testProducts[0].id);
    });

    it('should handle duplicate positions in request', async () => {
      const updateData = {
        category_id: testCategory.id,
        product_positions_data: [
          { id: testProducts[0].id, position: 1 },
          { id: testProducts[1].id, position: 1 },
        ],
      };

      await service.updateProductsPositionInCategoryV2(updateData, headers);

      const updatedRelations = await productCategoryRelationRepository.find({
        where: { category: { id: testCategory.id } },
        relations: ['product'],
      });

      // Both products should have position 1
      const position1Products = updatedRelations.filter(
        (r) => r.position === 1,
      );
      expect(position1Products).toHaveLength(2);
    });

    it('should handle null positions', async () => {
      const updateData = {
        category_id: testCategory.id,
        product_positions_data: [{ id: testProducts[0].id, position: null }],
      };

      await service.updateProductsPositionInCategoryV2(updateData, headers);

      const updatedRelation = await productCategoryRelationRepository.findOne({
        where: {
          category: { id: testCategory.id },
          product: { id: testProducts[0].id },
        },
      });

      expect(updatedRelation.position).toBeNull();
    });
  });
});

import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { UtilsModule } from 'src/utils/utils.module';
import { ProductFeedback } from 'src/database/entities/product/product-feedback.entity';
import { ProductFeedbackService } from './product-feedback.service';
import { ProductFeedbackController } from './product-feedback.controller';
import { CatalogProduct } from 'src/database/entities/product/main-product.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([ProductFeedback, CatalogProduct]),
    UtilsModule,
  ],
  providers: [ProductFeedbackService],
  controllers: [ProductFeedbackController],
  exports: [ProductFeedbackService],
})
export class ProductFeedbackModule {}

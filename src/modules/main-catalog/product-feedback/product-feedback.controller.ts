import {
  Controller,
  Body,
  Post,
  Get,
  Query,
  Headers,
  Param,
  ParseIntPipe,
} from '@nestjs/common';
import { ProductFeedbackService } from './product-feedback.service';
import { ProductFeedbackDto } from './dto/product-feedback.dto';

@Controller('v1/catalog-admin/product-feedback')
export class ProductFeedbackController {
  constructor(
    private readonly productFeedbackService: ProductFeedbackService,
  ) {}

  @Post('/:productId')
  async addProductFeedbackDetails(
    @Param('productId', ParseIntPipe) productId: number,
    @Body() body: ProductFeedbackDto,
    @Headers()
    headers: {
      Authorization: string;
    },
  ) {
    return await this.productFeedbackService.addProductFeedbackDetails(
      productId,
      body,
      headers,
    );
  }

  @Get()
  async getProductFeedbackList(
    @Query('page') page?: number,
    @Query('size') size?: number,
  ) {
    let requestBody = {
      pagination: {
        page,
        size,
      },
    };
    return await this.productFeedbackService.getProductFeedbackDetails(
      requestBody,
    );
  }
}

import {
  Injectable,
  InternalServerErrorException,
  BadRequestException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { LoggerService } from 'src/utils/logger-service';
import { ExternalApiHelper } from 'src/utils/external-api-helper';
import env from 'src/config/env';
import { ProductFeedback } from 'src/database/entities/product/product-feedback.entity';
import { ProductFeedbackDto } from './dto/product-feedback.dto';
import { CatalogProduct } from 'src/database/entities/product/main-product.entity';

@Injectable()
export class ProductFeedbackService {
  constructor(
    @InjectRepository(ProductFeedback)
    private readonly productFeedbackRepository: Repository<ProductFeedback>,
    @InjectRepository(CatalogProduct)
    private readonly catalogProductRepository: Repository<CatalogProduct>,
    private readonly logger: LoggerService,
    private readonly externalApiHelper: ExternalApiHelper,
  ) {}

  async addProductFeedbackDetails(
    productId: number,
    body: ProductFeedbackDto,
    headers,
  ) {
    try {
      const authToken = headers.authorization;
      let customerInfo;

      let existingProduct = await this.catalogProductRepository.findOne({
        where: { id: productId },
      });

      if (!existingProduct) {
        throw new BadRequestException('Invalid product id');
      }

      if (authToken) {
        customerInfo =
          await this.externalApiHelper.fetchCustomerInfoInfoHelper(authToken);
      }

      let { product_price, product_quality, other_feedback } = body;

      await this.productFeedbackRepository.save({
        price: product_price || null,
        quality: product_quality?.trim() || null,
        other_feedback: other_feedback?.trim() || null,
        user: customerInfo ? customerInfo.email : null,
        product: existingProduct,
      });

      return { message: 'Product feedback saved' };
    } catch (err) {
      this.logger.error('Failed to save product feedback', err);
      if (err.status === 400) {
        throw err;
      } else {
        throw new InternalServerErrorException(
          'Failed to save product feedback',
        );
      }
    }
  }

  async getProductFeedbackDetails(body) {
    try {
      let { pagination } = body;

      let queryConditions: any = {
        relations: ['product.catalogProductFlatRelations'],
      };

      queryConditions['skip'] =
        ((pagination?.page || 1) - 1) * env.sqlQueryResultsize;
      queryConditions['take'] = pagination?.size
        ? pagination.size
        : env.sqlQueryResultsize;

      let feedbackData = await this.productFeedbackRepository.findAndCount({
        ...queryConditions,
        order: { id: 'DESC' },
        select: {
          id: true,
          price: true,
          quality: true,
          other_feedback: true,
          user: true,
          product: {
            id: true,
            sku: true,
            catalogProductFlatRelations: {
              id: true,
              name: true,
            },
          },
        },
      });

      let feedbackDataArray = feedbackData[0];
      let feedbackDataCount = feedbackData[1];

      let modifiedData = feedbackDataArray.map((item) => {
        let data = {
          id: item.id,
          price: item.price,
          quality: item.quality,
          other_feedback: item.other_feedback,
          user: item.user,
          sku: item.product.sku,
          name: item.product.catalogProductFlatRelations.name,
        };
        return data;
      });

      let countPerPage = pagination?.page
        ? pagination.size
        : env.sqlQueryResultsize;

      return {
        item_count: feedbackDataCount,
        pages_count: Math.ceil(feedbackDataCount / countPerPage),
        page_no: pagination?.page,
        page_size: feedbackDataArray.length,
        items: modifiedData,
      };
    } catch (err) {
      this.logger.error('Failed to fetch product feedback data', err);
      if (err.status === 400) {
        throw err;
      } else {
        throw new InternalServerErrorException(
          'Failed to fetch product feedback data',
        );
      }
    }
  }
}

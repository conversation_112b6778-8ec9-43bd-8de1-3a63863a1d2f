import { Injectable, CanActivate, ExecutionContext } from '@nestjs/common';
import { GqlExecutionContext, GraphQLExecutionContext } from '@nestjs/graphql';
import env from 'src/config/env';

@Injectable()
export class ApiKeyGraphqlGuard implements CanActivate {
  canActivate(context: GraphQLExecutionContext): boolean {
    const ctx = GqlExecutionContext.create(context);
    let request = ctx.getContext().req;
    const apiKey = request.headers['x-api-key'];
    return apiKey === env.apiKey;
  }
}

import { Injectable, CanActivate, ExecutionContext } from '@nestjs/common';
import { ExternalApiHelper } from 'src//utils/external-api-helper';
import { GqlExecutionContext } from '@nestjs/graphql';
import config from '../config/env';

@Injectable()
export class AuthGuard implements CanActivate {
  constructor(private readonly externalApiHelper: ExternalApiHelper) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    try {
      const gqlContext = GqlExecutionContext.create(context);
      const isHttp = context.getType() === 'http';
      if (isHttp) {
        const req = context.switchToHttp().getRequest();
        const apiKey = req?.headers?.['x-api-key'];
        if (apiKey === config.apiKey) {
          return true;
        }
        const token = this.getUserToken(req.headers.authorization);
        if (!token) return false;
        const response = await this.externalApiHelper.getCustomerDetails(token);
        if (!response) return false;
        req.customer = response;
        req.customerId = response.id;
        req.customerEmail = response.email;
        return true;
      } else {
        const ctx = gqlContext.getContext();
        const token = this.getUserToken(ctx.req.headers.authorization);
        if (!token) return false;
        const response = await this.externalApiHelper.getCustomerDetails(token);
        if (!response) return false;
        ctx.req.customer = response;
        ctx.req.customerId = response.id;
        ctx.req.customerEmail = response.email;
        return true;
      }
    } catch (error) {
      return false;
    }
  }

  getUserToken(authHeader: string) {
    const token = authHeader?.split(' ')[1];
    if (token === 'null') return undefined;
    return token;
  }
}

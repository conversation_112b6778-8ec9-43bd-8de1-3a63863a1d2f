import {
  Injectable,
  CanActivate,
  ExecutionContext,
  BadRequestException,
} from '@nestjs/common';

@Injectable()
export class ValidateHeadersGuard implements CanActivate {
  //   constructor(private requiredHeaders: string[]) {}

  canActivate(context: ExecutionContext): boolean {
    const request = context.switchToHttp().getRequest();
    const headers = request.headers['admin_identifier'];

    if (headers == undefined) {
      throw new BadRequestException(
        `admin_identifier must be included in headers `,
      );
    }

    return true;
  }
}

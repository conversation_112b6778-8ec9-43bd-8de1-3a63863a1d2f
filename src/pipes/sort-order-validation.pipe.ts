import {
  PipeTransform,
  Injectable,
  ArgumentMetadata,
  BadRequestException,
} from '@nestjs/common';

@Injectable()
export class SortOrderValidationPipe implements PipeTransform {
  transform(value: any, metadata: ArgumentMetadata) {
    if (typeof value !== 'string') {
      throw new BadRequestException(`${metadata.data} must be a string`);
    }

    const validSortOrders = ['ASC', 'DESC'];
    if (!validSortOrders.includes(value.toUpperCase())) {
      return 'ASC';
    }

    return value.toUpperCase();
  }
}

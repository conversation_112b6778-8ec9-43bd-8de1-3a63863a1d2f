import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import * as express from 'express';
import env from './config/env';
import { ValidationPipe } from '@nestjs/common';


async function bootstrap() {
  const app = await NestFactory.create(AppModule);

  //Increase Payload size limit to 10 mb
  app.use(express.json({ limit: '10mb' }));

  app.useGlobalPipes(
    new ValidationPipe({
      transform: true,
    }),
  );
  app.enableCors({ origin: '*', allowedHeaders: '*', methods: '*' });


  await app.listen(env.port);
}
bootstrap();

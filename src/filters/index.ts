import {
  Catch,
  ExceptionFilter,
  HttpException,
  ArgumentsHost,
  Logger,
  HttpStatus,
} from '@nestjs/common';
import { Request, Response } from 'express';
import { QueryFailedError } from 'typeorm';

@Catch()
export class HttpExceptionFilter implements ExceptionFilter {
  catch(exception: HttpException, host: ArgumentsHost) {
    const ctxType = host.getType();
    if (ctxType === 'http') {
      const ctx = host.switchToHttp();
      const response = ctx.getResponse<Response>();
      const request = ctx.getRequest<Request>();

      const status = exception.getStatus
        ? exception.getStatus()
        : HttpStatus.INTERNAL_SERVER_ERROR;

      if (exception instanceof QueryFailedError) {
        const driverError = exception.driverError;
        if (driverError && driverError.code === 'ER_DUP_ENTRY') {
          response.status(HttpStatus.CONFLICT).json({
            statusCode: HttpStatus.CONFLICT,
            message:
              driverError.sqlMessage ||
              'Duplicate entry detected. The value for unique key already exists.',
            error: 'Conflict',
          });
          return;
        }
      }

      if (status === HttpStatus.INTERNAL_SERVER_ERROR) {
        console.error(exception);
      }
      console.error(exception);

      Logger.error(
        `${request.method} ${request.url}`,
        JSON.stringify(exception['response']),
        'ExceptionFilter',
      );

      // Add debugging to check what's in the response
      console.log('Exception response:', exception['response']);
      console.log('Exception status:', status);
      
      if (/sequelize/i.test(exception['name'])) {
        response.status(status).json({
          ...exception,
        });
      } 
      // Special handling for BadRequestException (status 400)
      else if (status === 400) {
        const errorResponse = exception['response'];
        const message = typeof errorResponse === 'object' && errorResponse?.message 
          ? errorResponse.message 
          : (typeof errorResponse === 'string' ? errorResponse : 'Bad Request');
            
        response.status(status).json({
          statusCode: status,
          message: message,
          error: 'Bad Request'
        });
      }
      else {
        response.status(status).json(
          // Make sure we have a valid response even if exception['response'] is empty
          exception['response'] ? 
            {...exception['response']} : 
            {statusCode: status, message: exception.message || 'Error occurred'}
        );
      }
    }
  }
}

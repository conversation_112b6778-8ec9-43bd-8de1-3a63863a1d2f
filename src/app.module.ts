import { Module, ValidationPipe } from '@nestjs/common';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { APP_GUARD, APP_PIPE, APP_FILTER } from '@nestjs/core';
import { ApiKeyGuard } from './guards/api-key.guards';
import { HttpExceptionFilter } from './filters';
import { GraphQLModule } from '@nestjs/graphql';
import { ScheduleModule } from '@nestjs/schedule';
import { ApolloDriver, ApolloDriverConfig } from '@nestjs/apollo';
import { TypeOrmModule } from '@nestjs/typeorm';
import { join } from 'path';
import { MulterModule } from '@nestjs/platform-express';
import ormconfig from './database/orm.config';
import { CatalogProductModule } from './modules/main-catalog/catalog-product/catalog-product.module';
import { LoggerService } from './utils/logger-service';
import { CatalogCategoryModule } from './modules/main-catalog/catalog-category/catalog-category.module';
import { ProductsTagModule } from './modules/main-catalog/products-tag/products-tag.module';
import { ProductsAttachmentModule } from './modules/main-catalog/product-attachment/products-attachment.module';
import { ProductSuggestionModule } from './modules/main-catalog/product-suggestion/product-suggestion.module';
import { ProductFeedbackModule } from './modules/main-catalog/product-feedback/product-feedback.module';
import { ProductFaqModule } from './modules/main-catalog/product-faq/product-faq.module';
import { BrandsModule } from './modules/main-catalog/brands/brands.module';
import { CatalogSyncModule } from './modules/catalog-sync/catalog-sync.module';
import { UploadModule } from './modules/upload/upload.module';
import { UtilsModule } from 'src/utils/utils.module';

@Module({
  imports: [
    CatalogProductModule,
    CatalogCategoryModule,
    MulterModule.register({ dest: './uploads' }),
    TypeOrmModule.forRoot({ ...ormconfig }),
    GraphQLModule.forRoot<ApolloDriverConfig>({
      driver: ApolloDriver,
      playground: true,
      typePaths: ['./**/*.graphql'],
      definitions: {
        path: join(process.cwd(), 'src/graphql.ts'),
      },
    }),
    ProductsTagModule,
    ProductsAttachmentModule,
    // ProductSuggestionModule,
    // ProductFeedbackModule,
    // ProductFaqModule,
    ScheduleModule.forRoot(),
    BrandsModule,
    CatalogSyncModule,
    UploadModule,
  ],
  controllers: [AppController],
  providers: [
    AppService,
    LoggerService,
    {
      provide: APP_PIPE,
      useClass: ValidationPipe,
    },
    // {
    //   provide: APP_GUARD,
    //   useClass: ApiKeyGuard,
    // },
    {
      provide: APP_FILTER,
      useClass: HttpExceptionFilter,
    },
  ],
  exports: [LoggerService],
})
export class AppModule {}

// import * as AWSXRay from 'aws-xray-sdk';

// export const xrayMiddleware = (req, res, next) => {
//   const segment: any = AWSXRay.getSegment();

//   console.log(
//     `Current X-RAY-TRACE DETAILS : Request ID: ${segment.trace_id}, URL: ${req.originalUrl}, Method: ${req.method}`,
//   );

//   segment.addMetadata('requestId', req.id);
//   segment.addMetadata('url', req.originalUrl);
//   segment.addMetadata('method', req.method);
//   next();
// };

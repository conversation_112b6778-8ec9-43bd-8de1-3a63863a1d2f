type Query {
  getQuestions(
    search: SearchInput!
    pagination: QuestionsPagination!
  ): QuestionsOutput
}

type Mutation {
  addQuestionsAnswer(input: QuestionsInputs!): QuestionsOutputData!
  productQnaLikeDislike(input: LikeDislikeInput!): LikeDislike
}

input AnswerDataInput {
  value: String
  updated_by: String
  updated_at: String
}

input QuestionsInputs {
  question: String!
  product_id: Int!
  enable: Boolean!
  like: Int
  dislike: Int
  product_image: String
  product_name: String
  status: String
  customer_token: String
  user: String
  answer: AnswerDataInput
}

input SearchInput {
  search: String
  product_id: Int
  enable: String
}

input QuestionsPagination {
  rowsPerPage: Int
  pageNumber: Int
}

type QuestionsOutput {
  result: [QuestionsOutputData!]
  count: Int
}

type QuestionsOutputData {
  _id: String
  question: String
  answer: AnswerData
  product_id: Int
  enable: Boolean
  product_image: String
  product_name: String
  like: Int
  dislike: Int
  created_at: String
  status: String
  customer_token: String
  user: String
}

type AnswerData {
  value: String
  updated_by: String
  updated_at: String
}

type QuestionsOutputData {
  _id: String
  question: String
  answer: AnswerData
  product_id: Int
  enable: Boolean
  product_image: String
  product_name: String
  like: Int
  dislike: Int
  created_at: String
  status: String
  customer_token: String
  user: String
  is_like: Boolean
}

input LikeDislikeInput {
  question_id: Int!
  action: ActionType!
}

enum ActionType {
  LIKE
  DISLIKE
  REMOVE
}

type LikeDislike {
  id: Int
  customer_id: Int
  question_id: Int
  is_like: Boolean
  like_count: Int
  dislike_count: Int
}

import { TypeOrmModuleOptions } from '@nestjs/typeorm';
import config from '../config/env';
import { join } from 'path';

const ormconfig: TypeOrmModuleOptions = {
  type: 'mysql',
  host: config.database.host,
  port: config.database.port,
  username: config.database.username,
  password: config.database.password,
  database: config.database.database,
  entities: [
    join(__dirname, 'entities', 'product', '*.js'),
    join(__dirname, 'entities', 'category', '*.js'),
    join(__dirname, 'entities', 'brand', '*.js'),
    join(__dirname, 'entities', 'outbox', '*.js'),
  ],
  // subscribers: [CatalogProductSubscriber],
  logging: false,
  synchronize: false, // Auto-generate and apply database schema changes in development (avoid in production)
};

export default ormconfig;

import {
  Column,
  <PERSON>tity,
  PrimaryGeneratedColumn,
  PrimaryColumn,
  CreateDateColumn,
  UpdateDateColumn,
  Unique,
  DeleteDateColumn,
  OneToOne,
  JoinColumn,
  ManyToOne,
} from 'typeorm';
import { ProductAttributeName } from './product-attribute-name.entity';
import { CatalogProduct } from './main-product.entity';
import { transformDateToIST } from 'src/utils/curentDateAndTime';

@Entity('product_decimal_attribute')
export class DecimalAttributeValues {
  @PrimaryGeneratedColumn()
  id: number;

  @ManyToOne(() => ProductAttributeName)
  @JoinColumn({ name: 'attribute_id' })
  attribute: ProductAttributeName;

  @ManyToOne(
    () => CatalogProduct,
    (catalogProduct) => catalogProduct.decimalAttributeValuesRelations,
    { onDelete: 'CASCADE' },
  )
  @JoinColumn({ name: 'product_id' })
  product: CatalogProduct;

  @Column({ nullable: false, type: 'decimal', precision: 10, scale: 2 })
  value: number;

  @CreateDateColumn({
    type: 'datetime',
  })
  created_at: Date;
  @UpdateDateColumn({
    type: 'datetime',
  })
  updated_at: Date;

  constructor(input?: Partial<DecimalAttributeValues>) {
    if (input) Object.assign(this, input);
  }
}

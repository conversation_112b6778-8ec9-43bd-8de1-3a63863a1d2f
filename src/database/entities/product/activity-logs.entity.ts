import {
  <PERSON>umn,
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  JoinColumn,
  ManyToOne,
  OneToMany,
  Unique,
  UpdateDateColumn,
} from 'typeorm';
import { CatalogProduct } from './main-product.entity';
import { transformDateToIST } from 'src/utils/curentDateAndTime';
import { Activity } from './activity.entity';
import { EventsOutbox } from '../outbox/event-outbox.entity';

@Entity('activity_log')
@Unique(['activity', 'entity_id'])
export class ActivityLogs {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ nullable: false })
  entity_id: number;

  @ManyToOne(() => Activity, (activity) => activity.activityLogsRelations, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'activity_id' })
  activity: Activity;

  @Column({ nullable: true, type: 'text' })
  old_value: string;

  @Column({ nullable: true, type: 'text' })
  new_value: string;

  @Column({ nullable: true, default: false })
  revert: boolean;

  @Column({ nullable: true })
  revert_log_id: number;

  @CreateDateColumn({
    type: 'datetime',
    // transformer: {
    //   from: (value) => transformDateToIST(value),
    //   to: (value) => value,
    // },
  })
  created_at: Date;

  @UpdateDateColumn({
    type: 'datetime',
    // transformer: {
    //   from: (value) => transformDateToIST(value),
    //   to: (value) => value,
    // },
  })
  updated_at: Date;

  @OneToMany(() => EventsOutbox, (relation) => relation.activity_logs, {
    cascade: true,
    onDelete: 'CASCADE',
  })
  eventsOutboxRelation: EventsOutbox[];

  constructor(input?: Partial<ActivityLogs>) {
    if (input) Object.assign(this, input);
  }
}

import {
  <PERSON>umn,
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  PrimaryColumn,
  CreateDateColumn,
  OneToMany,
} from 'typeorm';
import { AttributesGroupRelation } from './attributes-group-relation.entity';
import { transformDateToIST } from 'src/utils/curentDateAndTime';

@Entity('product_attribute_group')
export class AttributesGroup {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ nullable: false })
  group_name: string;

  @Column()
  group_code: string;

  @Column()
  sort_order: number;

  @CreateDateColumn({
    type: 'datetime',
  })
  created_at: Date;

  @OneToMany(() => AttributesGroupRelation, (relation) => relation.group)
  attributeGroupRelations: AttributesGroupRelation[];

  constructor(input?: Partial<AttributesGroup>) {
    if (input) Object.assign(this, input);
  }
}

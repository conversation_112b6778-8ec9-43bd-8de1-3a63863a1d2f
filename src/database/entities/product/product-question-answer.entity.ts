import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  OneToMany,
  CreateDateColumn,
  UpdateDateColumn,
  JoinC<PERSON>umn,
  Index,
} from 'typeorm';
import { LikeDislike } from './likes-dislikes.entity';
import { CatalogProduct } from './main-product.entity';

@Entity('product_question_answer')
@Index('IDX_PRODUCT_ID', ['product_id'])
export class QuestionAnswer {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ nullable: true })
  customer_id: number;

  @Column({ nullable: true })
  customer_email: string;

  @Column({ name: 'product_id', type: 'int' })
  product_id: number;

  @Column({ nullable: true })
  admin_email: string;

  @Column({ default: false })
  @Index()
  status: boolean;

  @Column('text', { nullable: false })
  question_text: string;

  @Column('text')
  answer_text: string;

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;

  @ManyToOne(() => CatalogProduct, (catalogProduct) => catalogProduct.questions)
  @JoinColumn({ name: 'product_id' })
  product: CatalogProduct;

  @OneToMany(() => LikeDislike, (likeDislike) => likeDislike.question, {
    onDelete: 'CASCADE',
  })
  likes_dislikes: LikeDislike[];
}

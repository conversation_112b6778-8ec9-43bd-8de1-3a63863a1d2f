import {
  Column,
  <PERSON>tity,
  PrimaryGeneratedColumn,
  PrimaryColumn,
  CreateDateColumn,
  UpdateDateColumn,
  Unique,
  DeleteDateColumn,
  OneToOne,
  JoinColumn,
  ManyToOne,
} from 'typeorm';
import { CatalogProduct } from './main-product.entity';
import { transformDateToIST } from 'src/utils/curentDateAndTime';

@Entity('product_media_gallary')
export class MediaGallary {
  @PrimaryGeneratedColumn()
  id: number;

  @ManyToOne(() => CatalogProduct)
  @JoinColumn({ name: 'product_id' })
  product: CatalogProduct;

  @Column({ nullable: false })
  value: string;

  @Column({ nullable: false, default: false })
  is_disabled: boolean;

  @Column({ default: 0 })
  position: number;

  @Column({ nullable: true })
  image_tags: string;

  @CreateDateColumn({
    type: 'datetime',
  })
  created_at: Date;
  @UpdateDateColumn({
    type: 'datetime',
  })
  updated_at: Date;

  constructor(input?: Partial<MediaGallary>) {
    if (input) Object.assign(this, input);
  }
}

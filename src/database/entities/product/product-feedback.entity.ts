import {
  <PERSON><PERSON>ty,
  Column,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { CatalogProduct } from './main-product.entity';

@Entity('product_feedback')
export class ProductFeedback {
  @PrimaryGeneratedColumn()
  id: string;

  @ManyToOne(() => CatalogProduct)
  @JoinColumn({ name: 'product_id' })
  product: CatalogProduct;

  @Column({ type: 'float', nullable: true })
  price: number;

  @Column({ type: 'text', nullable: true })
  quality: string;

  @Column({ type: 'text', nullable: true })
  other_feedback: string;

  @Column({ nullable: true })
  user: string;

  @CreateDateColumn({
    type: 'datetime',
  })
  created_at: Date;

  constructor(input?: Partial<ProductFeedback>) {
    if (input) Object.assign(this, input);
  }
}

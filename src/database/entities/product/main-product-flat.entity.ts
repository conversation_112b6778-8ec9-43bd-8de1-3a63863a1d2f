import {
  Column,
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  PrimaryColumn,
  CreateDateColumn,
  UpdateDateColumn,
  Unique,
  OneToMany,
  JoinColumn,
  ManyToOne,
  OneToOne,
} from 'typeorm';
import { CatalogProduct } from './main-product.entity';
import { transformDateToIST } from 'src/utils/curentDateAndTime';

@Entity('catalog_product_flat')
@Unique(['sku'])
export class CatalogProductFlat {
  @PrimaryGeneratedColumn()
  id: number;

  @OneToOne(
    () => CatalogProduct,
    (catalogProduct) => catalogProduct.catalogProductFlatRelations,
  )
  @JoinColumn({ name: 'product_id' })
  product: CatalogProduct;

  @Column({ nullable: false })
  sku: string;

  @Column({ nullable: false })
  status: boolean;

  @Column({ nullable: false })
  type_id: string;

  @CreateDateColumn({
    type: 'datetime',
  })
  created_at: Date;
  @UpdateDateColumn({
    type: 'datetime',
  })
  updated_at: Date;

  @Column({ nullable: false })
  name: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({ type: 'text', nullable: true })
  short_description: string;

  @Column({
    nullable: false,
    type: 'decimal',
    precision: 10,
    scale: 2,
    default: 0,
    transformer: {
      from: (value) => parseFloat(value),
      to: (value) => value,
    },
  })
  price: number;

  @Column({
    nullable: true,
    type: 'decimal',
    precision: 10,
    scale: 2,
    transformer: {
      from: (value) => parseFloat(value),
      to: (value) => value,
    },
  })
  special_price: number;

  @Column({
    type: 'datetime',
    nullable: true,
  })
  special_from_date: Date;

  @Column({
    type: 'datetime',
    nullable: true,
  })
  special_to_date: Date;

  @Column({
    nullable: true,
    type: 'decimal',
    precision: 10,
    scale: 2,
    transformer: {
      from: (value) => parseFloat(value),
      to: (value) => value,
    },
  })
  weight: number;

  @Column({ nullable: true })
  manufacturer: number;

  @Column({ nullable: true })
  image: string;

  @Column({ nullable: true })
  small_image: string;

  @Column({ nullable: true })
  swatch_image: string;

  @Column({ nullable: true })
  thumbnail: string;

  @Column({
    type: 'datetime',
    nullable: true,
  })
  news_from_date: Date;

  @Column({
    type: 'datetime',
    nullable: true,
  })
  news_to_date: Date;

  @Column({ nullable: true })
  url_key: string;

  @Column({ nullable: false })
  visibility: number;

  @Column({ nullable: true })
  country_of_manufacture: string;

  @Column({
    nullable: true,
    type: 'decimal',
    precision: 10,
    scale: 2,
    transformer: {
      from: (value) => parseFloat(value),
      to: (value) => value,
    },
  })
  msrp: number;

  @Column({ nullable: true })
  tax_class_id: number;

  @Column({ type: 'text', nullable: true })
  key_specifications: string;

  @Column({ type: 'text', nullable: true })
  features: string;

  @Column({ type: 'text', nullable: true })
  htext: string;

  @Column({ type: 'text', nullable: true })
  packaging: string;

  @Column({ type: 'text', nullable: true })
  other_info: string;

  @Column({ nullable: true })
  hvideo: string;

  @Column({ nullable: true })
  hsn_code: string;

  @Column({ nullable: true })
  is_cod: boolean;

  @Column({ type: 'text', nullable: true })
  warranty: string;

  @Column({ type: 'text', nullable: true })
  product_faq: string;

  @Column({ type: 'text', nullable: true })
  meta_keyword: string;

  @Column({ nullable: true })
  meta_title: string;

  @Column({ nullable: true })
  meta_description: string;

  @Column({ nullable: true })
  reward_point_product: number;

  @Column({ nullable: true })
  international_active: boolean;

  @Column({
    nullable: true,
    type: 'decimal',
    precision: 10,
    scale: 2,
    transformer: {
      from: (value) => parseFloat(value),
      to: (value) => value,
    },
  })
  average_rating: number;

  @Column({ nullable: true })
  rating_count: number;

  @Column({ nullable: true })
  return_period: number;

  @Column({ nullable: true })
  dispatch_days: number;

  @Column({
    type: 'datetime',
    nullable: true,
  })
  pd_expiry_date: Date;

  @Column({ nullable: true })
  gtin: string;

  @Column({
    nullable: true,
    type: 'decimal',
    precision: 10,
    scale: 2,
    transformer: {
      from: (value) => parseFloat(value),
      to: (value) => value,
    },
  })
  dentalkart_custom_fee: number;

  @Column({ nullable: true })
  demo_available: boolean;

  constructor(input?: Partial<CatalogProductFlat>) {
    if (input) Object.assign(this, input);
  }
}

import {
  Column,
  <PERSON>tity,
  PrimaryGeneratedColumn,
  PrimaryColumn,
  CreateDateColumn,
  UpdateDateColumn,
  Unique,
  DeleteDateColumn,
  OneToOne,
  JoinColumn,
  ManyToOne,
} from 'typeorm';
import { ProductAttributeName } from './product-attribute-name.entity';
import { CatalogProduct } from './main-product.entity';
import { transformDateToIST } from 'src/utils/curentDateAndTime';

@Entity('product_varchar_attribute')
export class StringAttributeValues {
  @PrimaryGeneratedColumn()
  id: number;

  @ManyToOne(() => ProductAttributeName)
  @JoinColumn({ name: 'attribute_id' })
  attribute: ProductAttributeName;

  @ManyToOne(
    () => CatalogProduct,
    (catalogProduct) => catalogProduct.stringAttributeValuesRelations,
    { onDelete: 'CASCADE' },
  )
  @JoinColumn({ name: 'product_id' })
  product: CatalogProduct;

  @Column({ nullable: true })
  value: string;

  @CreateDateColumn({
    type: 'datetime',
  })
  created_at: Date;
  @UpdateDateColumn({
    type: 'datetime',
  })
  updated_at: Date;

  constructor(input?: Partial<StringAttributeValues>) {
    if (input) Object.assign(this, input);
  }
}

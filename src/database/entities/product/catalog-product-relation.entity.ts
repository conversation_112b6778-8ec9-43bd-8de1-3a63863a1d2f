import {
  Column,
  <PERSON>tity,
  PrimaryGeneratedColumn,
  PrimaryColumn,
  CreateDateColumn,
  UpdateDateColumn,
  Unique,
  DeleteDateColumn,
  OneToOne,
  JoinColumn,
  ManyToOne,
} from 'typeorm';
import { CatalogProduct } from './main-product.entity';
import { IsInt } from 'class-validator';
import { transformDateToIST } from 'src/utils/curentDateAndTime';

export enum RelationTypes {
  UPSELL = 'upsell',
  CROSSSELL = 'crosssell',
  ASSOCIATED = 'associated',
  RELATED = 'related',
}

@Entity('catalog_product_relation')
export class CatalogProductRelation {
  @PrimaryGeneratedColumn()
  id: number;

  @ManyToOne(
    () => CatalogProduct,
    (catalogProduct) => catalogProduct.parentRelations,
    { onDelete: 'CASCADE' },
  )
  @JoinColumn({ name: 'parent_id' })
  parent: CatalogProduct;

  @ManyToOne(
    () => CatalogProduct,
    (catalogProduct) => catalogProduct.childRelations,
    { onDelete: 'CASCADE' },
  )
  @JoinColumn({ name: 'child_id' })
  child: CatalogProduct;

  @Column({ nullable: false })
  relation_type: string;

  @IsInt()
  @Column()
  position: number;

  @CreateDateColumn({
    type: 'datetime',
  })
  created_at: Date;
  @UpdateDateColumn({
    type: 'datetime',
  })
  updated_at: Date;
}

import {
  Column,
  <PERSON><PERSON>ty,
  PrimaryGeneratedColumn,
  PrimaryColumn,
  CreateDateColumn,
  UpdateDateColumn,
  Unique,
  DeleteDateColumn,
  OneToOne,
  JoinColumn,
  OneToMany,
} from 'typeorm';
import { AttributesGroupRelation } from './attributes-group-relation.entity';
import { ProductAttributesOptions } from './attribute-options.entity';
import { IntegerAttributeValues } from './integer-attribute-values.entity';
import { BooleanAttributeValues } from './boolean-attribute-values.entity';
import { DecimalAttributeValues } from './decimal-attribute-values.entity';
import { StringAttributeValues } from './string-attibute-values.entity';
import { TextAttributeValues } from './text-attribute-values.entity';
import { DateAttributeValues } from './date-attribute-values.entity';
import { transformDateToIST } from 'src/utils/curentDateAndTime';

export enum TypeId {
  SIMPLE = 'simple',
  GROUPED = 'grouped',
  VIRTUAL = 'virtual',
}

@Entity('product_attribute')
@Unique(['id'])
export class ProductAttributeName {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ nullable: false })
  code: string;

  @Column({ nullable: false })
  label: string;

  @Column({ nullable: true })
  apply_to: string;

  @Column({ nullable: false, default: false })
  is_unique: boolean;

  @Column({ nullable: false, default: false })
  is_required: boolean;

  @Column({ nullable: false, default: false })
  is_sortable: boolean;

  @Column({ nullable: false, default: false })
  is_filterable: boolean;

  @Column({ nullable: false, default: true })
  is_editable: boolean;

  @Column({ nullable: false, default: true })
  is_deletable: boolean;

  @Column({ nullable: true })
  default_value: string;

  @Column({ nullable: false })
  backend_type: string;

  @Column({ nullable: false })
  frontend_input: string;

  @OneToMany(() => AttributesGroupRelation, (relation) => relation.attribute)
  attributeNameRelations: AttributesGroupRelation[];

  @OneToMany(() => ProductAttributesOptions, (relation) => relation.attribute)
  attributeOptionsRelations: ProductAttributesOptions[];

  @OneToMany(() => IntegerAttributeValues, (relation) => relation.attribute)
  integerAttributeValuesRelations: IntegerAttributeValues[];

  @OneToMany(() => BooleanAttributeValues, (relation) => relation.product)
  booleanAttributeValuesRelations: BooleanAttributeValues[];

  @OneToMany(() => DateAttributeValues, (relation) => relation.product)
  dateAttributeValuesRelations: DateAttributeValues[];

  @OneToMany(() => DecimalAttributeValues, (relation) => relation.product)
  decimalAttributeValuesRelations: DecimalAttributeValues[];

  @OneToMany(() => StringAttributeValues, (relation) => relation.product)
  stringAttributeValuesRelations: StringAttributeValues[];

  @OneToMany(() => TextAttributeValues, (relation) => relation.product)
  textAttributeValuesRelations: TextAttributeValues[];

  @CreateDateColumn({
    type: 'datetime',
  })
  created_at: Date;
  @UpdateDateColumn({
    type: 'datetime',
  })
  updated_at: Date;

  constructor(input?: Partial<ProductAttributeName>) {
    if (input) Object.assign(this, input);
  }
}

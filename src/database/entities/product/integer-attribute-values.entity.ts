import {
  Column,
  <PERSON>tity,
  PrimaryGeneratedColumn,
  PrimaryColumn,
  CreateDateColumn,
  UpdateDateColumn,
  Unique,
  DeleteDateColumn,
  OneToOne,
  JoinColumn,
  ManyToOne,
} from 'typeorm';
import { ProductAttributeName } from './product-attribute-name.entity';
import { CatalogProduct } from './main-product.entity';
import { ProductAttributesOptions } from './attribute-options.entity';
import { transformDateToIST } from 'src/utils/curentDateAndTime';

@Entity('product_integer_attribute')
export class IntegerAttributeValues {
  @PrimaryGeneratedColumn()
  id: number;

  @ManyToOne(() => ProductAttributeName)
  @JoinColumn({ name: 'attribute_id' })
  attribute: ProductAttributeName;

  @ManyToOne(
    () => CatalogProduct,
    (catalogProduct) => catalogProduct.integerAttributeValuesRelations,
    { onDelete: 'CASCADE' },
  )
  @JoinColumn({ name: 'product_id' })
  product: CatalogProduct;

  @Column({ nullable: false })
  value: number;

  // @ManyToOne(() => ProductAttributesOptions)
  // @JoinColumn({ name: 'options_id' })
  // options: ProductAttributesOptions;

  @CreateDateColumn({
    type: 'datetime',
  })
  created_at: Date;

  @UpdateDateColumn({
    type: 'datetime',
  })
  updated_at: Date;

  constructor(input?: Partial<IntegerAttributeValues>) {
    if (input) Object.assign(this, input);
  }
}

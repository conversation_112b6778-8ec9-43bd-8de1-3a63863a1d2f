import {
  Column,
  <PERSON>tity,
  PrimaryGeneratedColumn,
  PrimaryColumn,
  CreateDateColumn,
  UpdateDateColumn,
  Unique,
  DeleteDateColumn,
  OneToOne,
  JoinColumn,
  ManyToOne,
} from 'typeorm';
import { CatalogProduct } from './main-product.entity';
import { IsInt } from 'class-validator';
import { transformDateToIST } from 'src/utils/curentDateAndTime';

@Entity('product_tier_price')
export class TierPrices {
  @PrimaryGeneratedColumn()
  id: number;

  @ManyToOne(
    () => CatalogProduct,
    (catalogProduct) => catalogProduct.tierPricesRelations,
    { onDelete: 'CASCADE' },
  )
  @JoinColumn({ name: 'product_id' })
  product: CatalogProduct;

  @Column({ nullable: false })
  value: number;

  @Column({ nullable: true, default: 'ALL GROUPS' })
  customer_group: string;

  @Column({ nullable: true, default: 'Fixed' })
  price_type: string;

  @IsInt()
  @Column({ nullable: false })
  quantity: number;

  @CreateDateColumn({
    type: 'datetime',
  })
  created_at: Date;
  @UpdateDateColumn({
    type: 'datetime',
  })
  updated_at: Date;
  constructor(input?: Partial<TierPrices>) {
    if (input) Object.assign(this, input);
  }
}

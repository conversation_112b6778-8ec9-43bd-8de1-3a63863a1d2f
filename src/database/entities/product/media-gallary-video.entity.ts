import {
  Column,
  <PERSON>tity,
  PrimaryGeneratedColumn,
  PrimaryColumn,
  CreateDateColumn,
  UpdateDateColumn,
  Unique,
  DeleteDateColumn,
  OneToOne,
  JoinColumn,
  ManyToOne,
} from 'typeorm';
import { CatalogProduct } from './main-product.entity';
import { transformDateToIST } from 'src/utils/curentDateAndTime';

@Entity('product_media_gallary_video')
export class MediaGallaryVideo {
  @PrimaryGeneratedColumn()
  id: number;

  @ManyToOne(() => CatalogProduct)
  @JoinColumn({ name: 'product_id' })
  product: CatalogProduct;

  @Column({ nullable: false })
  value: string;

  @Column()
  url: string;

  @Column()
  title: string;

  @Column({ type: 'text' })
  description: string;

  @Column({ nullable: false, default: false })
  is_disabled: boolean;

  @Column({ default: 0, nullable: false })
  position: number;

  @CreateDateColumn({
    type: 'datetime',
  })
  created_at: Date;
  @UpdateDateColumn({
    type: 'datetime',
  })
  updated_at: Date;

  constructor(input?: Partial<MediaGallaryVideo>) {
    if (input) Object.assign(this, input);
  }
}

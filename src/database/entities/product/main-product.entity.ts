import {
  Column,
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  PrimaryColumn,
  CreateDateColumn,
  UpdateDateColumn,
  DeleteDateColumn,
  Unique,
  OneToOne,
  OneToMany,
  JoinColumn,
  InsertEvent,
  AfterInsert,
  ManyToMany,
} from 'typeorm';
import { CatalogProductRelation } from './catalog-product-relation.entity';
import { MediaGallary } from './media-gallary.entity';
import { MediaGallaryVideo } from './media-gallary-video.entity';
import { IntegerAttributeValues } from './integer-attribute-values.entity';
import { BooleanAttributeValues } from './boolean-attribute-values.entity';
import { DateAttributeValues } from './date-attribute-values.entity';
import { DecimalAttributeValues } from './decimal-attribute-values.entity';
import { StringAttributeValues } from './string-attibute-values.entity';
import { TextAttributeValues } from './text-attribute-values.entity';
import { TierPrices } from './tier-prices.entity';
import { InventoryAttributes } from './inventory-attributes-values.entity';
import { ProductCategoryRelation } from '../category/product-category-relation.entity';
import { CatalogProductFlat } from './main-product-flat.entity';
import { transformDateToIST } from 'src/utils/curentDateAndTime';
import { SkuUpdateRecord } from './sku-update-record.entity';
import { ProductsTag } from './product-tag.entity';
import { ProductsAttachment } from './product-attachment.entity';
import { ProductStockAlert } from './product-stock-alert.entity';
import { ProductFeedback } from './product-feedback.entity';
import { QuestionAnswer } from './product-question-answer.entity';

@Entity('catalog_product')
@Unique(['sku'])
export class CatalogProduct {
  @PrimaryGeneratedColumn()
  readonly id: number;

  @Column({ nullable: false })
  sku: string;

  @Column({ nullable: false })
  status: boolean;

  @Column({ nullable: false })
  type_id: string;

  @OneToMany(() => CatalogProductRelation, (relation) => relation.parent, {
    cascade: true,
    onDelete: 'CASCADE',
  })
  parentRelations: CatalogProductRelation[];

  @OneToMany(() => CatalogProductRelation, (relation) => relation.child, {
    cascade: true,
    onDelete: 'CASCADE',
  })
  childRelations: CatalogProductRelation[];

  @OneToMany(() => MediaGallary, (relation) => relation.product, {
    cascade: true,
    onDelete: 'CASCADE',
  })
  productImageRelation: MediaGallary[];

  @OneToMany(() => MediaGallaryVideo, (relation) => relation.product, {
    cascade: true,
    onDelete: 'CASCADE',
  })
  productVideoRelation: MediaGallaryVideo[];

  @OneToMany(() => IntegerAttributeValues, (relation) => relation.product, {
    cascade: true,
    onDelete: 'CASCADE',
  })
  integerAttributeValuesRelations: IntegerAttributeValues[];

  @OneToMany(() => BooleanAttributeValues, (relation) => relation.product, {
    cascade: true,
    onDelete: 'CASCADE',
  })
  booleanAttributeValuesRelations: BooleanAttributeValues[];

  @OneToMany(() => ProductCategoryRelation, (relation) => relation.product, {
    cascade: true,
    onDelete: 'CASCADE',
  })
  productCategoryRelations: ProductCategoryRelation[];

  @OneToMany(() => DateAttributeValues, (relation) => relation.product, {
    cascade: true,
    onDelete: 'CASCADE',
  })
  dateAttributeValuesRelations: DateAttributeValues[];

  @OneToMany(() => DecimalAttributeValues, (relation) => relation.product, {
    cascade: true,
    onDelete: 'CASCADE',
  })
  decimalAttributeValuesRelations: DecimalAttributeValues[];

  @OneToMany(() => StringAttributeValues, (relation) => relation.product, {
    cascade: true,
    onDelete: 'CASCADE',
  })
  stringAttributeValuesRelations: StringAttributeValues[];

  @OneToMany(() => TextAttributeValues, (relation) => relation.product, {
    cascade: true,
    onDelete: 'CASCADE',
  })
  textAttributeValuesRelations: TextAttributeValues[];

  @OneToMany(() => TierPrices, (relation) => relation.product, {
    cascade: true,
    onDelete: 'CASCADE',
  })
  tierPricesRelations: TierPrices[];

  @OneToMany(() => SkuUpdateRecord, (relation) => relation.product, {
    cascade: true,
    onDelete: 'CASCADE',
  })
  skuUpdateRecordRelations: SkuUpdateRecord[];

  @OneToMany(() => ProductStockAlert, (relation) => relation.product, {
    cascade: true,
    onDelete: 'CASCADE',
  })
  productStockAlertRelations: ProductStockAlert[];

  @OneToMany(() => ProductFeedback, (relation) => relation.product, {
    cascade: true,
    onDelete: 'CASCADE',
  })
  productFeedbackRelations: ProductFeedback[];

  @OneToOne(() => InventoryAttributes, (relation) => relation.product, {
    cascade: true,
    onDelete: 'CASCADE',
  })
  inventoryAttributesRelations: InventoryAttributes;

  @OneToOne(() => CatalogProductFlat, (relation) => relation.product, {
    cascade: true,
    onDelete: 'SET NULL',
  })
  catalogProductFlatRelations: CatalogProductFlat;

  @CreateDateColumn({
    type: 'datetime',
  })
  created_at: Date;
  @UpdateDateColumn({
    type: 'datetime',
  })
  updated_at: Date;

  @DeleteDateColumn({ type: 'datetime' })
  deleted_at: Date;

  @ManyToMany(() => ProductsTag, (productsTag) => productsTag.products)
  tags: ProductsTag[];

  @ManyToMany(
    () => ProductsAttachment,
    (productsAttachment) => productsAttachment.products,
  )
  attachments: ProductsAttachment[];

  @OneToMany(() => QuestionAnswer, (questionAnswer) => questionAnswer.product)
  questions: QuestionAnswer[];
}

import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  ManyToOne,
  Unique,
  UpdateDateColumn,
  JoinColumn,
  Index,
} from 'typeorm';
import { QuestionAnswer } from './product-question-answer.entity';

@Entity('qna_likes_dislikes')
@Index('IDX_QUESTION_ID', ['question_id'])
@Index('IDX_CUSTOMER_ID', ['customer_id'])
@Unique(['customer_id', 'question_id'])
export class LikeDislike {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  customer_id: number;

  @Column({ name: 'question_id', type: 'int' })
  question_id: number;

  @Column({ type: 'boolean' })
  is_like: boolean;

  @CreateDateColumn({
    type: 'datetime',
  })
  created_at: Date;

  @UpdateDateColumn({
    type: 'datetime',
  })
  updated_at: Date;

  @ManyToOne(
    () => QuestionAnswer,
    (questionAnswer) => questionAnswer.likes_dislikes,
  )
  @JoinColumn({ name: 'question_id' })
  question: QuestionAnswer;
}

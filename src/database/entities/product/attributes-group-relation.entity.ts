import {
  Column,
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  PrimaryColumn,
  CreateDateColumn,
  OneToOne,
  JoinColumn,
  ManyToMany,
  OneToMany,
  ManyToOne,
  Unique,
} from 'typeorm';
import { AttributesGroup } from './attributes-group.entity';
import { ProductAttributeName } from './product-attribute-name.entity';
import { transformDateToIST } from 'src/utils/curentDateAndTime';

@Entity('product_attribute_group_relation')
@Unique(['attribute'])
export class AttributesGroupRelation {
  @PrimaryGeneratedColumn()
  id: number;

  @ManyToOne(() => AttributesGroup)
  @JoinColumn({ name: 'group_id' })
  group: AttributesGroup;

  @ManyToOne(() => ProductAttributeName)
  @JoinColumn({ name: 'attribute_id' })
  attribute: ProductAttributeName;

  @Column()
  sort_order: number;

  @CreateDateColumn({
    type: 'datetime',
  })
  created_at: Date;

  constructor(input?: Partial<AttributesGroupRelation>) {
    if (input) Object.assign(this, input);
  }
}

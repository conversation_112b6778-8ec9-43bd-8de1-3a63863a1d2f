import {
  Column,
  Entity,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  UpdateDateColumn,
  Unique,
} from 'typeorm';

export enum EntityTypeEnum {
  PRODUCT = 'product',
  CATEGORY = 'category',
  URL_REWRITE = 'url_rewrite',
}

@Entity('catalog_url_rewrite')
@Unique(['request_path'])
export class UrlRewrites {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ nullable: false })
  entity_id: number;

  @Column({
    type: 'enum',
    enum: EntityTypeEnum,
    default: EntityTypeEnum.PRODUCT,
  })
  entity_type: EntityTypeEnum;

  @Column({ nullable: false })
  redirect_type: number;

  @Column({ nullable: false })
  request_path: string;

  @Column({ nullable: false })
  target_path: string;

  @CreateDateColumn({
    type: 'datetime',
  })
  created_at: Date;

  @UpdateDateColumn({ type: 'datetime' })
  updated_at: Date;

  constructor(input?: Partial<UrlRewrites>) {
    if (input) Object.assign(this, input);
  }
}

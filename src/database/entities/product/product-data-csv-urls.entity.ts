import { transformDateToIST } from 'src/utils/curentDateAndTime';
import {
  Column,
  Entity,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';

export enum CsvGenerateStatus {
  PENDING = 'pending',
  COMPLETE = 'complete',
}

@Entity('catalog_export_queue')
export class ProductDataCsvUrls {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ nullable: false })
  filename: string;

  @Column({ nullable: true })
  report_url: string;

  @Column({
    type: 'enum',
    enum: CsvGenerateStatus,
    default: CsvGenerateStatus.PENDING,
  })
  status: CsvGenerateStatus; // Use the enum type

  @CreateDateColumn({
    type: 'datetime',
  })
  created_at: Date;

  @UpdateDateColumn({
    type: 'datetime',
  })
  updated_at: Date;

  constructor(input?: Partial<ProductDataCsvUrls>) {
    if (input) Object.assign(this, input);
  }
}

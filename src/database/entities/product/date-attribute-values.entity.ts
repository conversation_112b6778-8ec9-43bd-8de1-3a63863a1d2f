import {
  Column,
  <PERSON>tity,
  PrimaryGeneratedColumn,
  PrimaryColumn,
  CreateDateColumn,
  UpdateDateColumn,
  Unique,
  DeleteDateColumn,
  OneToOne,
  JoinColumn,
  ManyToOne,
} from 'typeorm';
import { ProductAttributeName } from './product-attribute-name.entity';
import { CatalogProduct } from './main-product.entity';
import { transformDateToIST } from 'src/utils/curentDateAndTime';

@Entity('product_date_attribute')
export class DateAttributeValues {
  @PrimaryGeneratedColumn()
  id: number;

  @ManyToOne(() => ProductAttributeName)
  @JoinColumn({ name: 'attribute_id' })
  attribute: ProductAttributeName;

  @ManyToOne(
    () => CatalogProduct,
    (catalogProduct) => catalogProduct.dateAttributeValuesRelations,
    { onDelete: 'CASCADE' },
  )
  @JoinColumn({ name: 'product_id' })
  product: CatalogProduct;

  @Column({ nullable: false, type: 'datetime' })
  value: Date;

  @CreateDateColumn({
    type: 'datetime',
  })
  created_at: Date;
  @UpdateDateColumn({
    type: 'datetime',
  })
  updated_at: Date;

  constructor(input?: Partial<DateAttributeValues>) {
    if (input) Object.assign(this, input);
  }
}

import {
  Column,
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  UpdateDateColumn,
  Unique,
  DeleteDateColumn,
  OneToOne,
  JoinColumn,
  ManyToOne,
} from 'typeorm';
import { CatalogProduct } from './main-product.entity';

@Entity('sku_update_record')
export class SkuUpdateRecord {
  @PrimaryGeneratedColumn()
  id: number;

  @ManyToOne(() => CatalogProduct)
  @JoinColumn({ name: 'product_id' })
  product: CatalogProduct;

  @Column({ nullable: false })
  old_sku: string;

  @Column({ nullable: false })
  status: boolean;

  @Column({ nullable: true })
  new_sku: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  error: string;

  @Column({ nullable: false })
  modified_by: string;

  @CreateDateColumn({
    type: 'datetime',
  })
  created_at: Date;
  @UpdateDateColumn({
    type: 'datetime',
  })
  updated_at: Date;

  constructor(input?: Partial<SkuUpdateRecord>) {
    if (input) Object.assign(this, input);
  }
}

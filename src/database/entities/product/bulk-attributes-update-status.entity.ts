import { transformDateToIST } from 'src/utils/curentDateAndTime';
import {
  Column,
  Entity,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';

export enum AttributesUpdateStatus {
  PENDING = 'pending',
  COMPLETE = 'complete',
}

@Entity('catalog_import_queue')
export class BulkAttributesUpdateStatus {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ nullable: false })
  filename: string;

  @Column({
    type: 'enum',
    enum: AttributesUpdateStatus,
    default: AttributesUpdateStatus.PENDING,
  })
  status: AttributesUpdateStatus;

  @Column({
    type: 'text',
    nullable: true,
  })
  report_url: string;

  @Column({
    type: 'text',
    nullable: true,
  })
  original_file_url: string;

  @CreateDateColumn({
    type: 'datetime',
    // transformer: {
    //   from: (value) => transformDateToIST(value),
    //   to: (value) => value,
    // },
  })
  created_at: Date;

  @UpdateDateColumn({
    type: 'datetime',
    // transformer: {
    //   from: (value) => transformDateToIST(value),
    //   to: (value) => value,
    // },
  })
  updated_at: Date;

  constructor(input?: Partial<BulkAttributesUpdateStatus>) {
    if (input) Object.assign(this, input);
  }
}

import {
  Entity,
  Column,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToMany,
  JoinTable,
  DeleteDateColumn,
} from 'typeorm';
import { CatalogProduct } from './main-product.entity';

export enum TagType {
  IMAGE = 'image',
  TEXT = 'text',
}

export enum TagPosition {
  TOP_LEFT = 'top_left',
  BOTTOM_RIGHT = 'bottom_right',
}

@Entity('product_tags')
export class ProductsTag {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ default: false })
  status: boolean;

  @Column()
  name: string;

  @Column({ unique: true })
  unique_code: string;

  @Column()
  value: string;

  @Column({
    type: 'enum',
    enum: TagType,
  })
  tag_type: TagType;

  @Column({
    type: 'enum',
    enum: TagPosition,
  })
  position: TagPosition;

  @Column({ nullable: true })
  description: string;

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;

  @DeleteDateColumn()
  deleted_at: Date;

  @ManyToMany(() => CatalogProduct, (catalogProduct) => catalogProduct.tags)
  @JoinTable()
  products: CatalogProduct[];
}

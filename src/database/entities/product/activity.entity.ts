import {
  Column,
  <PERSON>tity,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  JoinColumn,
  ManyToOne,
  OneToMany,
} from 'typeorm';
import { transformDateToIST } from 'src/utils/curentDateAndTime';
import { ActivityLogs } from './activity-logs.entity';

export enum EntityTypeEnum {
  PRODUCT = 'product',
  CATEGORY = 'category',
  URL_REWRITE = 'url_rewrite',
}

@Entity('activity')
export class Activity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ nullable: false })
  user: string;

  @Column({
    type: 'enum',
    enum: EntityTypeEnum,
    default: EntityTypeEnum.PRODUCT,
  })
  entity: EntityTypeEnum;

  @Column()
  activity_type: string;

  @Column({ nullable: true, type: 'text' })
  user_meta_info: string;

  @CreateDateColumn({
    type: 'datetime',
  })
  created_at: Date;

  @OneToMany(() => ActivityLogs, (relation) => relation.activity, {
    cascade: true,
    onDelete: 'CASCADE',
  })
  activityLogsRelations: ActivityLogs[];

  constructor(input?: Partial<Activity>) {
    if (input) Object.assign(this, input);
  }
}

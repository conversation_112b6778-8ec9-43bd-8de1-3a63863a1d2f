import {
  <PERSON><PERSON>ty,
  Column,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToMany,
  JoinTable,
  DeleteDateColumn,
} from 'typeorm';
import { CatalogProduct } from './main-product.entity';

@Entity('product_attachments')
export class ProductsAttachment {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ default: false })
  status: boolean;

  @Column()
  description: string;

  @Column()
  active_device: string;

  @Column()
  customer_group: string;

  @Column()
  thumbnail: string;

  @Column()
  url: string;

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;

  @DeleteDateColumn()
  deleted_at: Date;

  @ManyToMany(
    () => CatalogProduct,
    (catalogProduct) => catalogProduct.attachments,
  )
  @JoinTable()
  products: CatalogProduct[];
}

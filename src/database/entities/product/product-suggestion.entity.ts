import {
  <PERSON><PERSON><PERSON>,
  Column,
  PrimaryGeneratedColumn,
  CreateDateColumn,
} from 'typeorm';

@Entity('product_suggestion')
export class ProductSuggestion {
  @PrimaryGeneratedColumn()
  id: string;

  @Column({ nullable: true })
  searched_key: string;

  @Column({ nullable: true })
  product_name: string;

  @Column({ nullable: true })
  brand: string;

  @Column({ nullable: true })
  user: string;

  @Column({ type: 'text', nullable: true })
  comment: string;

  @CreateDateColumn({
    type: 'datetime',
  })
  created_at: Date;

  constructor(input?: Partial<ProductSuggestion>) {
    if (input) Object.assign(this, input);
  }
}

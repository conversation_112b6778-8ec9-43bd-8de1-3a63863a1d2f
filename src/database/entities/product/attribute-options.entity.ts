import {
  Column,
  <PERSON>tity,
  PrimaryGeneratedColumn,
  PrimaryColumn,
  CreateDateColumn,
  UpdateDateColumn,
  Unique,
  DeleteDateColumn,
  OneToOne,
  JoinColumn,
  ManyToOne,
  OneToMany,
} from 'typeorm';
import { ProductAttributeName } from './product-attribute-name.entity';
import { IntegerAttributeValues } from './integer-attribute-values.entity';
import { transformDateToIST } from 'src/utils/curentDateAndTime';

@Entity('product_attribute_option')
export class ProductAttributesOptions {
  @PrimaryGeneratedColumn()
  id: number;

  @ManyToOne(() => ProductAttributeName)
  @JoinColumn({ name: 'attribute_id' })
  attribute: ProductAttributeName;

  @Column({ nullable: false })
  value: string;

  // @OneToMany(() => IntegerAttributeValues, (relation) => relation.options)
  // IntegerAttributeOptionsRelation: IntegerAttributeValues[];

  @CreateDateColumn({
    type: 'datetime',
  })
  created_at: Date;
  @UpdateDateColumn({
    type: 'datetime',
  })
  updated_at: Date;
  constructor(input?: Partial<ProductAttributesOptions>) {
    if (input) Object.assign(this, input);
  }
}

import {
  Column,
  Entity,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  UpdateDateColumn,
  JoinColumn,
  ManyToOne,
} from 'typeorm';
import { CatalogProduct } from './main-product.entity';

@Entity('product_stock_alert')
export class ProductStockAlert {
  @PrimaryGeneratedColumn()
  id: number;

  @ManyToOne(() => CatalogProduct)
  @JoinColumn({ name: 'product_id' })
  product: CatalogProduct;

  @Column({ nullable: false })
  customer_name: string;

  @Column({ nullable: false })
  notification_status: boolean;

  @Column({ nullable: true })
  customer_email: string;

  @CreateDateColumn({
    type: 'datetime',
  })
  created_at: Date;
  @UpdateDateColumn({
    type: 'datetime',
  })
  updated_at: Date;

  constructor(input?: Partial<ProductStockAlert>) {
    if (input) Object.assign(this, input);
  }
}

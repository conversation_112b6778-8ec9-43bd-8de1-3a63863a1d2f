import {
  Column,
  <PERSON>tity,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  JoinColumn,
  ManyToOne,
  UpdateDateColumn,
  Index,
} from 'typeorm';
import { ActivityLogs } from '../product/activity-logs.entity';

export enum EntityType {
  PRODUCT = 'catalog_product',
  CATEGORY = 'catalog_category',
  URL_REWRITE = 'catalog_url_rewrite',
}

@Entity('catalog_event_outbox')
export class EventsOutbox {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ nullable: false })
  entity_id: number;

  @Index()
  @Column({
    type: 'enum',
    enum: EntityType,
    default: EntityType.PRODUCT,
  })
  entity_type: EntityType;

  @Column({ nullable: false, default: false })
  is_published: boolean;

  @Column()
  process_count: number;

  @Column({ nullable: false, default: false })
  received_by_es: boolean;

  @CreateDateColumn({
    type: 'datetime',
  })
  created_at: Date;

  @UpdateDateColumn({
    type: 'datetime',
  })
  updated_at: Date;

  @ManyToOne(
    () => ActivityLogs,
    (activityLogs) => activityLogs.eventsOutboxRelation,
    { onDelete: 'CASCADE' },
  )
  @JoinColumn({ name: 'activity_logs_id' })
  activity_logs: ActivityLogs;

  constructor(input?: Partial<EventsOutbox>) {
    if (input) Object.assign(this, input);
  }
}

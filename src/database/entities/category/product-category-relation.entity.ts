import {
  Column,
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  PrimaryColumn,
  CreateDateColumn,
  OneToOne,
  JoinColumn,
  ManyToOne,
  UpdateDateColumn,
} from 'typeorm';
import { CatalogCategory } from './main-category.entity';
import { CatalogProduct } from '../product/main-product.entity';
import { IsInt } from 'class-validator';
import { transformDateToIST } from 'src/utils/curentDateAndTime';

@Entity('catalog_category_product')
export class ProductCategoryRelation {
  @PrimaryGeneratedColumn()
  id: number;

  @ManyToOne(() => CatalogCategory, (category) => category.categoryRelation, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'category_id' })
  category: CatalogCategory;

  // @ManyToOne(() => CatalogProduct, (product) => product.productCategoryRelations, {
  //   onDelete: 'CASCADE',
  // })

  @ManyToOne(
    () => CatalogProduct,
    (catalogProduct) => catalogProduct.productCategoryRelations,
    { onDelete: 'CASCADE' },
  )
  @JoinColumn({ name: 'product_id' })
  product: CatalogProduct;

  @Column({ nullable: true })
  position: number | null;

  // @IsInt()
  // @Column()
  // position:number;

  @CreateDateColumn({
    type: 'datetime',
  })
  created_at: Date;
  @UpdateDateColumn({
    type: 'datetime',
  })
  updated_at: Date;
}

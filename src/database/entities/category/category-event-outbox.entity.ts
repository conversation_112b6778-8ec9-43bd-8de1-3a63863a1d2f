import {
  <PERSON>umn,
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  JoinColumn,
  ManyToOne,
  UpdateDateColumn,
} from 'typeorm';
import { ActivityLogs } from '../product/activity-logs.entity';

@Entity('catalog_category_event_outbox')
export class CategoryEventsOutbox {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ nullable: false })
  entity_id: number;

  @Column({ nullable: false, default: false })
  is_published: boolean;

  @Column()
  process_count: number;

  @Column({ nullable: false, default: false })
  received_by_es: boolean;

  @CreateDateColumn({
    type: 'datetime',
  })
  created_at: Date;

  @UpdateDateColumn({
    type: 'datetime',
  })
  updated_at: Date;

  @ManyToOne(
    () => ActivityLogs,
    (activityLogs) => activityLogs.eventsOutboxRelation,
    { onDelete: 'CASCADE' },
  )
  @JoinColumn({ name: 'activity_logs_id' })
  activity_logs: ActivityLogs;

  constructor(input?: Partial<CategoryEventsOutbox>) {
    if (input) Object.assign(this, input);
  }
}

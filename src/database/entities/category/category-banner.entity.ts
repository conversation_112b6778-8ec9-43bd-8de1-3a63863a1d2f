import {
  Column,
  <PERSON><PERSON>ty,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';

export enum LandingPageType {
  PRODUCT = 'product',
  CATEGORY = 'category',
}

@Entity('category_banner')
export class CategoryBanner {
  @PrimaryGeneratedColumn()
  id: number;

  @ManyToOne('CatalogCategory', 'categoryBanners', {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'category_id' })
  category: any;

  @Column({ type: 'int', nullable: false })
  category_id: number;

  @Column({ type: 'int', default: 0 })
  sort_order: number;

  @Column({ type: 'varchar', length: 255, nullable: false })
  title: string;

  @Column({
    type: 'enum',
    enum: LandingPageType,
    nullable: false,
  })
  landing_page_type: LandingPageType;

  @Column({ type: 'varchar', length: 500, nullable: false })
  landing_page_url: string;

  @Column({ type: 'varchar', length: 500, nullable: true })
  web_url: string;

  @Column({ type: 'varchar', length: 500, nullable: true })
  tab_url: string;

  @Column({ type: 'varchar', length: 500, nullable: true })
  mobile_url: string;

  @Column({ type: 'varchar', length: 500, nullable: true })
  icon_url: string;

  @CreateDateColumn({
    type: 'datetime',
  })
  created_at: Date;

  @UpdateDateColumn({
    type: 'datetime',
  })
  updated_at: Date;

  constructor(input?: Partial<CategoryBanner>) {
    if (input) Object.assign(this, input);
  }
}

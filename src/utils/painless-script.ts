export function categoryPainlessScript(): string {
  return `
    for (int i = ctx._source.category_links.size() - 1; i >= 0; i--) {
      if (ctx._source.category_links[i].category_id == params.categoryId) {
        ctx._source.category_links.remove(i);
      }
    }
  `;
}

export function productPainlessScript(): string {
  return `
  for (int i = ctx._source.category_products.size() - 1; i >= 0; i--) {
    if (ctx._source.category_products[i].product_id == params.productId) {
      ctx._source.category_products.remove(i);
    }
  }
`;
}

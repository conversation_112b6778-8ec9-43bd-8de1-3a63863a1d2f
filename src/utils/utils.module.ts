import { Module } from '@nestjs/common';
import { S3Service } from './s3service';
import { LoggerService } from './logger-service';
import { ExternalApiHelper } from './external-api-helper';
import { ExternalApiCaller } from './external-api-caller';
import { HttpModule } from '@nestjs/axios';
import { EventsLogService } from './events-logging-service';
import { Activity } from 'src/database/entities/product/activity.entity';
import { TypeOrmModule } from '@nestjs/typeorm';
import { EventsOutbox } from 'src/database/entities/outbox/event-outbox.entity';
import { ActivityLogs } from 'src/database/entities/product/activity-logs.entity';

@Module({
  imports: [
    HttpModule,
    TypeOrmModule.forFeature([EventsOutbox, ActivityLogs, Activity]),
  ],
  controllers: [],
  providers: [
    S3Service,
    LoggerService,
    ExternalApiHelper,
    ExternalApiCaller,
    EventsLogService,
  ],
  exports: [
    S3Service,
    LoggerService,
    ExternalApiHelper,
    LoggerService,
    EventsLogService,
  ],
})
export class UtilsModule {}

export async function modifyCategoryProducts(jsonString) {
  try {
    // console.log(typeof jsonString);
    if (!jsonString) return [];

    const parsedData =
      typeof jsonString === 'string' ? JSON.parse(jsonString) : jsonString;

    if (!Array.isArray(parsedData)) {
      const result = Object.keys(parsedData).map((product_id) => ({
        product_id,
        position: parsedData[product_id],
      }));
      return result;
    } else {
      const result = parsedData.map((item) => ({
        product_id: Object.keys(item)[0],
        position: Object.values(item)[0],
      }));
      return result;
    }
  } catch (error) {
    console.error('Error parsing JSON:', error);
    return [];
  }
}

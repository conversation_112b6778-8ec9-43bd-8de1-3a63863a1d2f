import { format, formatInTimeZone } from 'date-fns-tz';

export function currentDateAndTime(): string {
  return new Date().toISOString().replace(/T/, ' ').replace(/\..+/, '');
}

export function formatDateString(_date: Date) {
  //for IST timing on the server we are adding 5 hours and 30 minutes in the date obj
  const date = new Date(_date);
  date.setHours(_date.getHours() + 5);
  date.setMinutes(_date.getMinutes() + 30);
  const day = String(date.getDate()).padStart(2, '0');
  const month = date.toLocaleString('default', {
    timeZone: 'Asia/Kolkata',
    month: 'short',
  });
  const year = date.getFullYear();
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  const seconds = String(date.getSeconds()).padStart(2, '0');

  return `${day}${month}${year}_${
    Number(hours) <= 12 ? hours : Number(hours) - 12
  }:${minutes}:${seconds}_${Number(hours) <= 12 ? 'AM' : 'PM'}`;
}

export function dateInBasicFormat(dateString) {
  const date = new Date(dateString);
  const day = date.getDate();
  const month = date.getMonth() + 1; // January is 0, so we add 1
  const year = date.getFullYear().toString().slice(-2); // Get the last two digits of the year

  // Pad single digit day and month with a leading zero
  const formattedDay = day < 10 ? '0' + day : day;
  const formattedMonth = month < 10 ? '0' + month : month;

  return `${formattedDay}/${formattedMonth}/${year}`;
}

export function transformDateToIST(value: string | Date): string {
  if (value == null) return null;
  let utcDate = new Date(value);
  return format(utcDate, 'yyyy-MM-dd HH:mm:ss');
  // return formatInTimeZone(utcDate, 'Asia/Kolkata', 'yyyy-MM-dd HH:mm:ss');
}

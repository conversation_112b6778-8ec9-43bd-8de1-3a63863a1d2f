import { Injectable } from '@nestjs/common';
import { createLogger, transports, format } from 'winston';

const { combine, timestamp, printf } = format;

const myFormat = printf(({ level, message, timestamp, label, stack }) => {
  return `${timestamp} [${level.toUpperCase()}] ${
    label ? `(${label})` : ''
  }: ${message} ${stack ? `(${stack})` : ''}`;
});

const logger = createLogger({
  format: combine(
    format.label({ label: 'Catalog-Service' }),
    format.errors({ stack: true }),
    format.splat(),
    format.simple(),
    timestamp(),
    myFormat,
  ),
  transports: [
    new transports.Console(),
    new transports.File({ filename: 'logs.log' }),
  ],
});

@Injectable()
export class LoggerService {
  log(message: string, line?: number) {
    const stack = new Error().stack.split('\n')[2].trim();
    const logMessage = line ? `${message} (Line: ${line})` : message;
    logger.info(logMessage, { stack });
  }

  error(message: string, trace: any, line?: number) {
    const stack = new Error().stack.split('\n')[2].trim();
    const logMessage = line ? `${message} (Line: ${line})` : message;
    // logger.error(logMessage, { stack });
    // Handle the trace parameter
    let errorDetails = '';
    if (trace) {
      if (trace instanceof Error) {
        errorDetails = `\nStack: ${trace.stack}`;
      } else {
        errorDetails = `\nDetails: ${JSON.stringify(trace, null, 2)}`;
      }
    }

    logger.error(`${logMessage}${errorDetails}`, { stack });
  }

  warn(message: string, line?: number) {
    const stack = new Error().stack.split('\n')[2].trim();
    const logMessage = line ? `${message} (Line: ${line})` : message;
    logger.warn(logMessage, { stack });
  }

  debug(message: string, line?: number) {
    const stack = new Error().stack.split('\n')[2].trim();
    const logMessage = line ? `${message} (Line: ${line})` : message;
    logger.debug(logMessage, { stack });
  }
}

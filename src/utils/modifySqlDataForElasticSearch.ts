export async function modifySqlDataForElasticSearch(product: any[]) {
  try {
    const excludedKeys = [
      'id',
      'sku',
      'status',
      'type_id',
      'created_at',
      'updated_at',
    ];

    const attributeArray = Object.keys(product[0].attributes_list)
      .filter((key) => !excludedKeys.includes(key))
      .map((key) => ({
        attribute_code: key,
        value: product[0]?.attributes_list[key] ?? '',
      }));

    const priceAttribute = await product[0].attributes_list.price;
    const nameAttribute = await product[0].attributes_list.name;
    const visibilityAttribute = await product[0].attributes_list.visibility;
    const statusAttribute = await product[0].status;
    const weightAttribute = await product[0].attributes_list.weight;

    let mappedData = {
      id: product[0].id,
      sku: product[0].sku,
      price: priceAttribute ? parseFloat(priceAttribute) : 0,
      name: nameAttribute,
      status: statusAttribute == true ? 1 : 0,
      visibility: visibilityAttribute ? visibilityAttribute : 1,
      type_id: product[0].type_id,
      created_at: product[0]?.created_at ?? new Date().toISOString(),
      updated_at: product[0].updated_at,
      weight: weightAttribute ? parseFloat(weightAttribute) : 0,
      tier_prices:
        product[0].tier_prices.length > 0
          ? product[0].tier_prices.map((e) => {
              return { ...e, customer_group_id: 0 };
            })
          : [],
      custom_attributes: attributeArray,
      extension_attributes: {
        stock_item: {
          ...product[0].inventory_details,
          backorders: product[0]?.inventory_details?.backorders == true ? 1 : 0,
        },
        category_links: product[0]?.category_associated.map((e) => {
          return { position: 0, category_id: String(e) };
        }),
      },
      product_links: product[0].product_links,
      media_gallery_entries:
        product[0].media_gallery_entries.length > 0
          ? await Promise.all(
              product[0]?.media_gallery_entries?.map(async (e) => {
                if (e.url === undefined) {
                  return {
                    ...e,
                    file: e.value.includes('.com/')
                      ? e.value.substring(e.value.indexOf('.com/') + 5)
                      : e.value,
                    media_type: 'image',
                    label: '',
                    disabled: e.is_disabled,
                    types: e.image_tags
                      ? e.image_tags
                          .split(',')
                          .map((tag) => tag.trim())
                          .filter((tag) => tag.length > 0)
                      : [],
                  };
                } else {
                  return {
                    ...e,
                    media_type: 'external-video',
                    label: '',
                    types: [],
                    file: e.value,
                    extension_attributes: {
                      video_content: {
                        media_type: 'external-video',
                        video_provider: '',
                        video_url: e.url,
                        video_title: e.title,
                        video_description: e.description,
                        video_metadata: '',
                      },
                    },
                  };
                }
              }),
            )
          : [],
    };

    // console.log(mappedData);

    return mappedData;
  } catch (error) {
    console.error('Error in mapping SQL data', error);
    return [];
  }
}

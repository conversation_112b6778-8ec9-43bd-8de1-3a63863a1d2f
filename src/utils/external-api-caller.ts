import { HttpService } from '@nestjs/axios';
import { Injectable, InternalServerErrorException } from '@nestjs/common';
import { firstValueFrom } from 'rxjs';

@Injectable()
export class ExternalApiCaller {
  constructor(private readonly httpService: HttpService) {}

  /**
   * Make a call to any GET method type API with specified headers
   * @param url API end url
   * @param headers header object
   * @returns
   */
  get = async (
    url: string,
    headers: { [key: string]: string },
    params?: { [key: string]: string | number },
  ): Promise<any> => {
    try {
      const response: any = await firstValueFrom(
        this.httpService.get(url, { headers, params, timeout: 0 }),
      );

      return [200, 201].indexOf(response.status) === -1 ? null : response?.data;
    } catch (error) {
      console.log('Error in get call', error);
      return null;
    }
  };

  /**
   * Make a call to any POST method type API with specified headers
   * and requested payload data
   * @param url API end url
   * @param headers header object
   * @param payload request body
   * @returns
   */
  post = async (
    url: string,
    headers: { [key: string]: string },
    payload: any,
    throwError?: boolean,
  ): Promise<any> => {
    try {
      const response: any = await firstValueFrom(
        this.httpService.post(url, payload, { headers }),
      );
      return [200, 201].indexOf(response.status) === -1 ? null : response?.data;
    } catch (error) {
      console.log('Error in post call', error);
      if (throwError) throw new InternalServerErrorException(error);
    }
  };
}

// import { Injectable } from '@nestjs/common';
// import { currentDateAndTime } from 'src/utils/curentDateAndTime';
// import env from 'src/config/env';
// // import { OpensearchService } from 'src/modules/elasticsearch-catalog/opensearch/opensearch.service';

// @Injectable()
// export class productGqlMapper {
//   // constructor(private readonly opensearchService: OpensearchService) {}

//   // private category_index_name = env.categoryIndexName;

//   // modifyCustomAttributes = async (products) => {
//   //   try {
//   //     const updatedProducts = await Promise.all(
//   //       products.map(async (product) => {
//   //         const combinedAttributes = {
//   //           ...product.custom_attributes,
//   //           parent_image_url: product.parent_image_url,
//   //           parent_url_key: product.parent_url_key,
//   //           parent_product_ids: product.parent_product_ids,
//   //           image_url: product.custom_attributes.image,
//   //         };

//   //         let mappedProductForGql = {
//   //           id: product.id,
//   //           sku: product.sku,
//   //           name: product.name,
//   //           type_id: product.type_id,
//   //           position: product.position,
//   //           weight: product.weight,
//   //           url_key: product.url_key,
//   //           manufacturer: product.manufacturer,
//   //           stock_item: await this.checkForStockAvailability(
//   //             product.stock_items,
//   //           ),
//   //           media_gallery: product.media_gallary_entries,
//   //           custom_attributes: combinedAttributes,
//   //           all_prices: await this.allPricesGqlModifier(product.all_prices),
//   //           categories: await this.modifyCategoryLinks(product.category_links),
//   //           product_links: product.product_links,
//   //           average_rating: product.average_rating,
//   //           review_count: product.review_count,
//   //         };
//   //         //console.log(da1.average_rating, da1.review_count, 'Stringify1');
//   //         return mappedProductForGql;
//   //       }),
//   //     );

//   //     // Return the modified productsproductsproducts

//   //     return updatedProducts;
//   //   } catch (error) {
//   //     console.error('Error:', error);
//   //     throw new Error('Failed to create modified products for graphql');
//   //   }
//   // };

//   // checkForStockAvailability = async (data) => {
//   //   if (data.qty <= 0 && data.backorders == 1) {
//   //     return {
//   //       is_in_stock: true,
//   //       max_sale_qty: data.max_sale_qty,
//   //       min_sale_qty: data.min_sale_qty,
//   //     };
//   //   } else {
//   //     return {
//   //       is_in_stock: data.is_in_stock,
//   //       max_sale_qty: data.max_sale_qty,
//   //       min_sale_qty: data.min_sale_qty,
//   //     };
//   //   }
//   // };

//   // allPricesGqlModifier = async (data) => {
//   //   let returnObj = {
//   //     mrp: data.price,
//   //     sell_price: data.sell_price,
//   //     currency_symbol: 'Rs',
//   //     tier_prices: data.tier_prices,
//   //   };

//   //   return returnObj;
//   // };

//   // calculateSellingPrice = async (data) => {
//   //   let currentDate = currentDateAndTime();
//   //   let sell_price;
//   //   if (!data.special_price) {
//   //     return (sell_price = parseFloat(data.price));
//   //   } else if (parseFloat(data.price) < parseFloat(data.special_price)) {
//   //     return (sell_price = parseFloat(data.price));
//   //   } else if (parseFloat(data.price) > parseFloat(data.special_price)) {
//   //     if (
//   //       (data.special_from_date || data.special_to_date) &&
//   //       (new Date(currentDate) > new Date(data.special_from_date) ||
//   //         new Date(currentDate) < new Date(data.special_to_date))
//   //     ) {
//   //       // console.log('Cond3');

//   //       sell_price = parseFloat(data.special_price);
//   //       return sell_price;
//   //     }
//   //   } else return (sell_price = parseFloat(data.price));
//   // };

//   // modifyCustomAttributesWhileSaving = async (data) => {
//   //   const result = {};

//   //   await data.forEach(({ attribute_code, value }) => {
//   //     switch (attribute_code) {
//   //       case 'avg_rating':
//   //         result[attribute_code] = parseFloat(value);
//   //         break;
//   //       case 'is_cod':
//   //         result[attribute_code] = value === '0' ? false : true;
//   //         break;
//   //       case 'international_active':
//   //         result[attribute_code] = value === '0' ? false : true;
//   //         break;
//   //       case 'demo_available':
//   //         result[attribute_code] = value === '0' ? false : true;
//   //         break;
//   //       case 'dispatch_days':
//   //         result[attribute_code] = parseInt(value, 10);
//   //         break;
//   //       case 'review_count':
//   //         result[attribute_code] = parseInt(value, 10);
//   //         break;
//   //       case 'msrp':
//   //         result[attribute_code] = parseFloat(value);
//   //         break;
//   //       case 'dentalkart_custom_fee':
//   //         result[attribute_code] = parseFloat(value);
//   //         break;
//   //       case 'reward_point_product':
//   //         result[attribute_code] = parseInt(value, 10);
//   //         break;

//   //       default:
//   //         result[attribute_code] = value;
//   //     }
//   //   });

//   //   return result;
//   // };

//   // modifyCategoryLinks = async (categoryLinks) => {
//   //   try {
//   //     let newCategoryLinksArray = [];
//   //     for await (const data of categoryLinks) {
//   //       try {
//   //         let checkExistingCategoryQuery = {
//   //           query: {
//   //             term: {
//   //               entity_id: parseInt(data.category_id, 10),
//   //             },
//   //           },
//   //         };
//   //         const checkExistingCategory = await this.opensearchService.search(
//   //           this.category_index_name,
//   //           checkExistingCategoryQuery,
//   //         );

//   //         // Check if any hits were found
//   //         if (checkExistingCategory.hits.hits.length > 0) {
//   //           let currentCategoryData =
//   //             checkExistingCategory.hits.hits[0]._source;

//   //           let currentCategoryLinkObject = {
//   //             ...data,
//   //             name: currentCategoryData.name,
//   //             url_path: currentCategoryData.url_path,
//   //           };

//   //           newCategoryLinksArray.push(currentCategoryLinkObject);
//   //         } else {
//   //           console.log(
//   //             `Category not found for entity_id: ${data.category_id}`,
//   //           );
//   //         }
//   //       } catch (err) {
//   //         console.log(
//   //           'Error in checking all the categories associated with the product',
//   //           err,
//   //         );
//   //       }
//   //     }
//   //     return newCategoryLinksArray;
//   //   } catch (err) {
//   //     console.log('Error while modifying product link', err);
//   //   }
//   // };
// }

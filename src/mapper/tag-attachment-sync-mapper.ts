import { Injectable } from '@nestjs/common';
import { TagType } from 'src/database/entities/product/product-tag.entity';

@Injectable()
export class SyncProductTagAttachmentMapper {
  syncProductsTagMapper(data: { [key: string]: any }, action: string) {
    return {
      tag_id: data.id,
      identifier: data.unique_code,
      products:
        data?.products?.map((product: any) => ({
          product_id: product.id,
          sort_order: 0,
        })) ?? [],
      status: data.status,
      tag_description: data.description,
      tag_title: data.name,
      image: data.tag_type === TagType.IMAGE ? data.value : null,
      position: data.position,
      tag_type: data.tag_type,
      value: data.value,
      action: action,
      created_at: new Date(data.created_at),
      updated_at: new Date(data.updated_at),
    };
  }

  syncProductAttachmentsMapper(data: { [key: string]: any }, action: string) {
    return {
      attach_id: data.id,
      title: data.description,
      icon: data.thumbnail,
      url: data.url,
      file: null,
      astatus: data.status,
      sort_order: data.sort_order ?? 0,
      product_id: data?.products?.map((data) => data.id) ?? [],
      customer_group: null,
      created_at: new Date(data.created_at),
      updated_at: new Date(data.updated_at),
      action: action,
    };
  }
}

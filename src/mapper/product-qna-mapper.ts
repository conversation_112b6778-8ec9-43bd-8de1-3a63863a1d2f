import { Injectable } from '@nestjs/common';
import { QuestionAnswer } from 'src/database/entities/product/product-question-answer.entity';
import {
  QuestionsOutputData,
  ProductQnaList,
} from 'src/modules/main-catalog/product-faq/interface/graphql-request';

@Injectable()
export class ProductQnaMapper {
  createQuestionGqlResponse(
    data: QuestionAnswer,
  ): Partial<QuestionsOutputData> {
    return {
      _id: data?.id?.toString() ?? null,
      question: data.question_text,
      answer: {
        value: data.answer_text,
        updated_at: data?.updated_at?.toISOString() ?? null,
        updated_by: data.admin_email,
      },
      product_id: data.product_id,
      enable: data.status,
    };
  }

  getGqlQuestionAnswersList(data: ProductQnaList[]): QuestionsOutputData[] {
    return data?.map((data) => {
      return {
        _id: data?.id?.toString() ?? null,
        question: data.question,
        answer: {
          value: data.answer,
          updated_at: data?.updated_at?.toISOString() ?? null,
          updated_by: data.admin_email,
        },
        product_id: data.product_id,
        enable: data.status,
        like: data.like_count,
        dislike: data.dislike_count,
        created_at: data?.created_at?.toISOString() ?? null,
        status: null,
        customer_token: null,
        user: null,
        product_image: null,
        product_name: null,
        is_like: data.is_like,
      };
    });
  }
}

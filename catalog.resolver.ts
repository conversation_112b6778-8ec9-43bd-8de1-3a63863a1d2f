// import { BadRequestException, UseGuards } from '@nestjs/common';
// import { Args, Context, Mutation, Resolver, Query } from '@nestjs/graphql';
// import { ApiKeyGraphqlGuard } from 'src/guards/api-key-gql.guards';
// // import { productGqlMapper } from 'src/mapper/product-gql-mapper';
// // import { SearchService } from 'src/modules/elasticsearch-catalog/search/search.service';
// // import { SearchService } from 'src/modules/search/search.service';

// @UseGuards(ApiKeyGraphqlGuard)
// @Resolver('Catalog')
// export class CatalogResolver {
//   constructor(
//     private readonly searchService: SearchService,
//     private readonly productgql: productGqlMapper,
//   ) {}

//   @Query()
//   async getProductsByIdsOrSku(
//     @Args()
//     request: { product_ids?: [number]; skus?: string[] },
//     @Context() context: any,
//   ) {
//     //console.log(JSON.stringify(request.product_ids), 'Resolver');
//     //console.log(JSON.stringify(request.sku));
//     //console.log(context.req.body.query, 'This is my context');

//     let gqlQuery = context.req.body.query;

//     let actualData, formattedData;

//     if (request.product_ids) {
//       const productIds = request.product_ids;
//       var productIdsAsString = productIds.map(String);
//     }

//     if (!request.product_ids && !request.skus) {
//       throw new BadRequestException('Please send either product_ids or sku');
//     }
//     let data;
//     request.product_ids
//       ? (data = { product_ids: productIdsAsString })
//       : (data = { skus: request.skus });

//     if (
//       gqlQuery.includes('average_rating') ||
//       gqlQuery.includes('review_count')
//     ) {
//       actualData = await this.searchService.getProductsData(data);
//       formattedData = await this.productgql.modifyCustomAttributes(actualData);

//       return formattedData;
//     } else {
//       actualData = await this.searchService.searchOnlyProductsData(data);
//       formattedData = await this.productgql.modifyCustomAttributes(actualData);

//       return formattedData;
//     }
//   }

//   @Query()
//   async getAssociatedChildProducts(
//     @Args()
//     request: { product_id: number },
//     @Context() context: any,
//   ) {
//     if (!request.product_id) {
//       throw new BadRequestException('Please send a valid product id');
//     }
//     const childProductDetails =
//       await this.searchService.getAssociatedChildProducts({
//         product_id: request.product_id,
//       });

//     let formattedData =
//       await this.productgql.modifyCustomAttributes(childProductDetails);
//     // const childProductDetails = await this.getProductsByIdsOrSku(
//     //   { skus: childSkuList },
//     //   context,
//     // );

//     // console.log(childProductDetails, 'CPD');
//     const isInStock = childProductDetails.some(
//       (e) => e.stock_items.is_in_stock === true,
//     );

//     const lowestSellPrice = childProductDetails.reduce(
//       (lowest, currentItem) => {
//         let currentPrice = currentItem.all_prices.sell_price;
//         return currentPrice < lowest ? currentPrice : lowest;
//       },
//       Infinity,
//     );

//     let response = {
//       items: formattedData,
//       parent_price: lowestSellPrice,
//       parent_stock_status: isInStock,
//     };

//     return response;
//   }

//   @Query()
//   async getProductsAggregatedData(
//     @Args()
//     request: { category_id: number },
//     @Context() context: any,
//   ) {
//     if (!request.category_id) {
//       throw new BadRequestException('Please send a valid category id');
//     }

//     return await this.searchService.getProductsAggregatedData({
//       category_id: request.category_id,
//     });
//   }

//   @Query()
//   async getCategoryProductsV3(
//     @Args()
//     request: { category_id: number; page_no: number; filter; sort },
//     @Context() context: any,
//   ) {
//     if (!request.category_id || !request.page_no) {
//       throw new BadRequestException(
//         'Please send a valid category id and page no',
//       );
//     }

//     return await this.searchService.getProductsfromCategoryId({
//       category_id: request.category_id,
//       page_no: request.page_no,
//       filter: request.filter,
//       sort: request.sort,
//     });
//   }
// }

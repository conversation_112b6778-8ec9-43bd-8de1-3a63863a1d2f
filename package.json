{"name": "catalog-service", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"prebuild": "<PERSON><PERSON><PERSON> dist", "build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "rimraf src/graphql.ts && nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/src/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json"}, "dependencies": {"@apollo/server": "^4.9.5", "@nestjs/apollo": "^12.0.10", "@nestjs/axios": "^3.0.2", "@nestjs/common": "^10.0.0", "@nestjs/core": "^10.0.0", "@nestjs/graphql": "^12.0.10", "@nestjs/platform-express": "^10.0.0", "@nestjs/schedule": "^4.1.0", "@nestjs/typeorm": "^10.0.2", "@opensearch-project/opensearch": "^2.4.0", "aws-sdk": "^2.1472.0", "aws-xray-sdk": "^3.9.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.0", "csv-parser": "^3.0.0", "date-fns-tz": "^3.1.3", "dotenv": "^16.3.1", "graphql": "^16.8.1", "multer": "^1.4.5-lts.1", "mysql2": "^3.9.1", "reflect-metadata": "^0.1.13", "rimraf": "^5.0.5", "rxjs": "^7.8.1", "ts-morph": "^20.0.0", "typeorm": "^0.3.20", "winston": "^3.12.0"}, "devDependencies": {"@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^10.0.0", "@types/express": "^4.17.17", "@types/jest": "^29.5.2", "@types/multer": "^1.4.11", "@types/node": "^20.3.1", "@types/supertest": "^2.0.12", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.42.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "jest": "^29.5.0", "prettier": "^3.0.0", "source-map-support": "^0.5.21", "supertest": "^6.3.3", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.1.3"}}
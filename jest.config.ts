import type { Config } from '@jest/types';
import { pathsToModuleNameMapper } from 'ts-jest';
// Import paths from tsconfig
import { compilerOptions } from './tsconfig.json';

const config: Config.InitialOptions = {
  preset: 'ts-jest',
  moduleFileExtensions: ['js', 'json', 'ts'],
  rootDir: '.',
  testRegex: '.*\\.spec\\.ts$',
  transform: {
    '^.+\\.(t|j)s$': 'ts-jest',
  },
  collectCoverageFrom: ['**/*.(t|j)s'],
  coverageDirectory: '../coverage',
  testEnvironment: 'node',
  // This is the important part - it maps your tsconfig paths to Jest paths
  moduleNameMapper: pathsToModuleNameMapper(compilerOptions.paths, {
    prefix: '<rootDir>/',
  }),
  // Make sure Jest can find modules
  modulePaths: ['<rootDir>'],
  // Make Type<PERSON> happy when importing JSON
  resolver: 'jest-ts-webcompat-resolver',
  maxWorkers: 1,
};

export default config;

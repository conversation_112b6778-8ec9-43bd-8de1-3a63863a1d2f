# input GetProductsInput {
#   product_ids: [String]
#   sku: [String]
# }

# input GetProductsFromCategoryInput {
#   category_id: String
# }

type ProductSource {
  id: Int
  sku: String
  position: String
  name: String
  type_id: String
  weight: Float
  manufacturer: String
  categories: [CategoryLink]
  stock_item: StockItems
  media_gallery: [MediaGallary]
  custom_attributes: CustomAttribute
  all_prices: AllPrices
  parent_image_url: String
  # parent_product_ids: [String]
  product_links: ProductLinks
  # user: String
  # parent_url_key: String
  review_count: Int
  average_rating: Float
  url_key: String
  tags: String
}

type AllPrices {
  mrp: Float
  sell_price: Float
  price: Float
  currency_symbol: String
  # special_price: String
  tier_prices: [TierPricesInput]
  # price: Int!
  # special_from_date: String
  # special_to_date: String
}

type CategoryLink {
  name: String
  position: Int
  url_path: String
  category_id: String
}

type TierPricesInput {
  value: Float
  qty: Int
}

type StockItems {
  is_in_stock: Boolean
  max_sale_qty: Float
  min_sale_qty: Float
  # item_id: String
  # backorders: Int
  min_qty: Int
  max_qty: Int
  # qty: Int
}

type CustomAttribute {
  parent_url_key: String
  parent_image_url: String
  parent_product_ids: [String]
  description: String
  short_description: String
  average_rating: Float
  review_count: Int
  is_cod: Boolean
  international_active: Boolean
  msrp: Float
  demo_available: Boolean
  pd_expiry_date: String
  dentalkart_custom_fee: Float
  dispatch_days: Int
  reward_point_product: Int
  meta_title: String
  meta_keyword: String
  meta_description: String
  image_url: String
  thumbnail: String
  tax_class_id: String
  image: String
}

type ProductLinks {
  related: [ProductLinkTypes]
  crosssell: [ProductLinkTypes]
  upsell: [ProductLinkTypes]
  associated: [ProductLinkTypes]
}

type ProductLinkTypes {
  link_type: String
  linked_product_sku: String
  linked_product_type: String
  position: String
  sku: String
}

type VideoContent {
  media_type: String
  video_provider: String
  video_url: String
  video_title: String
  video_description: String
  video_metadata: String
}

type AttributesVideoContent {
  video_content: VideoContent
}

type MediaGallary {
  media_type: String
  label: String
  position: Int
  types: [String]
  file: String
  extension_attributes: AttributesVideoContent
}

type CategoryData {
  products: [ProductSource]
  count: Int
}

type ProductsAggregatedData {
  min_price: Float
  max_price: Float
  unique_manufacturers: [String]
}

input CategoryProductsFilterInput {
  manufacturer: [String]
  price: CategoryPriceFilterInput
}

input CategoryPriceFilterInput {
  max_price: Float
  min_price: Float
}

input CategoryProductSortInput {
  name: SortEnum
  price: SortEnum
}

enum SortEnum {
  ASC
  DESC
}

type GetCategoryProductsOutput {
  category_id: Int
  description: String
  description_position: String
  Image: String
  items: [ProductSource]
  meta_description: String
  meta_keyword: String
  meta_title: String
  name: String
  page_no: Int
  product_count: Int
  url_key: String
}

type GetAssociatedChildProductsOutput {
  items: [ProductSource]
  parent_price: Float
  parent_stock_status: Boolean
}

type Query {
  getProductsByIdsOrSku(product_ids: [Int], skus: [String]): [ProductSource]
  getProductsByCategoryId(category_id: Int!): CategoryData
  getAssociatedChildProducts(product_id: Int): GetAssociatedChildProductsOutput
  getProductsAggregatedData(category_id: Int!): ProductsAggregatedData
  getCategoryProductsV3(
    category_id: Int!
    page_no: Int
    filter: CategoryProductsFilterInput
    sort: CategoryProductSortInput
  ): GetCategoryProductsOutput
}
